// 传输管理器演示程序
// 展示传输管理器的主要功能

use std::sync::Arc;
use tokio::time::{sleep, Duration};
use kvm_tunnel::{
    config::{StorageConfig, MemoryStorageConfig, DiskStorageConfig, MediaSoupConfig},
    storage::StorageManager,
    core::transport_manager::TransportManager,
    types::{
        TransportType, TransportConfig, TransportStatus,
        AddressConfig, PortRange, Protocol,
    },
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 KVM隧道传输管理器演示");
    println!("========================");

    // 创建存储管理器
    let storage_config = StorageConfig {
        memory: MemoryStorageConfig {
            max_sessions: 1000,
            max_transports: 5000,
            cleanup_interval_seconds: 300,
        },
        disk: DiskStorageConfig {
            base_path: "/tmp/kvm_tunnel_transport_demo".to_string(),
            max_log_size_mb: 10,
            log_retention_days: 7,
            sync_interval_seconds: 60,
        },
    };

    let storage_manager = Arc::new(StorageManager::new(storage_config).await?);
    println!("✅ 存储管理器创建成功");

    // 创建MediaSoup配置
    let mediasoup_config = MediaSoupConfig {
        worker_count: 4,
        max_connections_per_worker: 100,
        rtc_port_range: (10000, 20000),
        log_level: "warn".to_string(),
        log_tags: vec!["info".to_string(), "ice".to_string(), "dtls".to_string()],
        dtls_cert_file: Some("/tmp/dtls-cert.pem".to_string()),
        dtls_key_file: Some("/tmp/dtls-key.pem".to_string()),
    };

    // 创建传输管理器
    let transport_manager = Arc::new(TransportManager::new(
        mediasoup_config,
        storage_manager.clone(),
    ).await?);
    println!("✅ 传输管理器创建成功");

    // 启动传输管理器
    transport_manager.start().await?;
    println!("✅ 传输管理器启动成功");

    // 演示传输创建和管理
    demo_transport_creation(&transport_manager).await?;

    // 演示传输生命周期管理
    demo_transport_lifecycle(&transport_manager).await?;

    // 演示传输查询功能
    demo_transport_queries(&transport_manager).await?;

    // 演示传输统计功能
    demo_transport_statistics(&transport_manager).await?;

    // 停止传输管理器
    transport_manager.stop().await?;
    println!("✅ 传输管理器停止成功");

    println!("\n🎉 演示完成！");
    Ok(())
}

/// 演示传输创建和管理
async fn demo_transport_creation(transport_manager: &Arc<TransportManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📦 演示传输创建和管理");
    println!("--------------------");

    // 创建MediaSoup传输
    let mediasoup_config = TransportConfig {
        transport_type: TransportType::MediaSoup,
        addresses: vec![
            AddressConfig {
                ip: "*************".to_string(),
                port: PortRange::Range(10000, 10100),
                protocol: Protocol::UDP,
            },
        ],
        security: None,
        qos: None,
    };

    let mediasoup_id = transport_manager.create_transport(
        TransportType::MediaSoup,
        mediasoup_config,
    ).await?;
    println!("✅ MediaSoup传输创建成功: {}", mediasoup_id);

    // 创建TCP隧道传输
    let tcp_tunnel_config = TransportConfig {
        transport_type: TransportType::TcpTunnel,
        addresses: vec![
            AddressConfig {
                ip: "0.0.0.0".to_string(),
                port: PortRange::Single(8080),
                protocol: Protocol::TCP,
            },
        ],
        security: None,
        qos: None,
    };

    let tcp_tunnel_id = transport_manager.create_transport(
        TransportType::TcpTunnel,
        tcp_tunnel_config,
    ).await?;
    println!("✅ TCP隧道传输创建成功: {}", tcp_tunnel_id);

    // 创建UDP隧道传输
    let udp_tunnel_config = TransportConfig {
        transport_type: TransportType::UdpTunnel,
        addresses: vec![
            AddressConfig {
                ip: "0.0.0.0".to_string(),
                port: PortRange::Single(8081),
                protocol: Protocol::UDP,
            },
        ],
        security: None,
        qos: None,
    };

    let udp_tunnel_id = transport_manager.create_transport(
        TransportType::UdpTunnel,
        udp_tunnel_config,
    ).await?;
    println!("✅ UDP隧道传输创建成功: {}", udp_tunnel_id);

    // 创建组播传输
    let multicast_config = TransportConfig {
        transport_type: TransportType::Multicast,
        addresses: vec![
            AddressConfig {
                ip: "*********".to_string(),
                port: PortRange::Single(9999),
                protocol: Protocol::UDP,
            },
        ],
        security: None,
        qos: None,
    };

    let multicast_id = transport_manager.create_transport(
        TransportType::Multicast,
        multicast_config,
    ).await?;
    println!("✅ 组播传输创建成功: {}", multicast_id);

    // 验证传输数量
    let transport_count = transport_manager.transport_count().await;
    println!("📊 当前传输数量: {}", transport_count);

    Ok(())
}

/// 演示传输生命周期管理
async fn demo_transport_lifecycle(transport_manager: &Arc<TransportManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔄 演示传输生命周期管理");
    println!("----------------------");

    // 获取第一个传输进行生命周期演示
    let transports = transport_manager.list_transports().await?;
    if let Some(transport) = transports.first() {
        let transport_id = &transport.transport_id;
        
        println!("🔄 演示传输: {} (类型: {:?})", transport_id, transport.transport_type);

        // 启动传输
        transport_manager.start_transport(transport_id).await?;
        println!("✅ 传输启动成功");
        sleep(Duration::from_millis(500)).await;

        // 暂停传输
        transport_manager.pause_transport(transport_id).await?;
        println!("⏸️ 传输暂停成功");
        sleep(Duration::from_millis(500)).await;

        // 恢复传输
        transport_manager.resume_transport(transport_id).await?;
        println!("▶️ 传输恢复成功");
        sleep(Duration::from_millis(500)).await;

        // 停止传输
        transport_manager.stop_transport(transport_id).await?;
        println!("⏹️ 传输停止成功");
    }

    Ok(())
}

/// 演示传输查询功能
async fn demo_transport_queries(transport_manager: &Arc<TransportManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔍 演示传输查询功能");
    println!("------------------");

    // 列出所有传输
    let all_transports = transport_manager.list_transports().await?;
    println!("📋 所有传输列表:");
    for transport in &all_transports {
        println!("  - {}: {:?} (状态: {:?})", 
            transport.transport_id, 
            transport.transport_type, 
            transport.status
        );
    }

    // 按类型查询传输
    let mediasoup_transports = transport_manager.get_transports_by_type(TransportType::MediaSoup).await?;
    println!("\n📡 MediaSoup传输数量: {}", mediasoup_transports.len());

    let tcp_transports = transport_manager.get_transports_by_type(TransportType::TcpTunnel).await?;
    println!("🔗 TCP隧道传输数量: {}", tcp_transports.len());

    let udp_transports = transport_manager.get_transports_by_type(TransportType::UdpTunnel).await?;
    println!("📡 UDP隧道传输数量: {}", udp_transports.len());

    // 按状态查询传输
    let active_transports = transport_manager.get_transports_by_status(TransportStatus::Active).await?;
    println!("\n🟢 活跃传输数量: {}", active_transports.len());

    let closed_transports = transport_manager.get_transports_by_status(TransportStatus::Closed).await?;
    println!("🔴 已关闭传输数量: {}", closed_transports.len());

    // 查询特定传输详情
    if let Some(transport) = all_transports.first() {
        let transport_info = transport_manager.get_transport_info(&transport.transport_id).await?;
        println!("\n🔍 传输详情 ({}):", transport_info.transport_id);
        println!("  类型: {:?}", transport_info.transport_type);
        println!("  状态: {:?}", transport_info.status);
        println!("  创建时间: {}", transport_info.created_at.format("%Y-%m-%d %H:%M:%S"));
        println!("  地址数量: {}", transport_info.config.addresses.len());
    }

    Ok(())
}

/// 演示传输统计功能
async fn demo_transport_statistics(transport_manager: &Arc<TransportManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📊 演示传输统计功能");
    println!("------------------");

    let transport_count = transport_manager.transport_count().await;
    println!("📈 总传输数: {}", transport_count);

    // 按类型统计
    let all_transports = transport_manager.list_transports().await?;
    
    let mut type_counts = std::collections::HashMap::new();
    for transport in &all_transports {
        *type_counts.entry(transport.transport_type.clone()).or_insert(0) += 1;
    }

    println!("\n📊 按类型统计:");
    for (transport_type, count) in type_counts {
        println!("  {:?}: {}", transport_type, count);
    }

    // 按状态统计
    let mut status_counts = std::collections::HashMap::new();
    for transport in &all_transports {
        *status_counts.entry(transport.status).or_insert(0) += 1;
    }

    println!("\n📊 按状态统计:");
    for (status, count) in status_counts {
        println!("  {:?}: {}", status, count);
    }

    // 获取传输统计信息
    if let Some(transport) = all_transports.first() {
        match transport_manager.get_transport_stats(&transport.transport_id).await {
            Ok(stats) => {
                println!("\n📊 传输统计 ({}):", transport.transport_id);
                println!("  发送字节: {}", stats.bytes_sent);
                println!("  接收字节: {}", stats.bytes_received);
                println!("  发送包数: {}", stats.packets_sent);
                println!("  接收包数: {}", stats.packets_received);
                println!("  错误数量: {}", stats.error_count);
            }
            Err(e) => {
                println!("❌ 获取传输统计失败: {}", e);
            }
        }
    }

    Ok(())
}
