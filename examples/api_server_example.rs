use axum::{
    response::Json,
    routing::get,
    Router,
};
use serde_json;
use tokio::net::TcpListener;
use tracing::{info, Level};
use tracing_subscriber;

/// 简单的API服务器示例
/// 
/// 这个示例展示了如何创建一个基本的HTTP API服务器，
/// 提供健康检查和基本信息端点。
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .init();

    info!("启动API服务器示例...");

    // 创建路由
    let app = Router::new()
        .route("/", get(root))
        .route("/health", get(health_check))
        .route("/api/v1/info", get(get_info))
        .route("/api/v1/status", get(get_status));

    // 绑定到端口
    let listener = TcpListener::bind("127.0.0.1:8080").await?;
    let addr = listener.local_addr()?;
    
    info!("API服务器启动成功，监听地址: {}", addr);
    info!("可用端点:");
    info!("  GET /           - 根路径");
    info!("  GET /health     - 健康检查");
    info!("  GET /api/v1/info - 服务信息");
    info!("  GET /api/v1/status - 服务状态");
    info!("");
    info!("测试命令:");
    info!("  curl http://127.0.0.1:8080/health");
    info!("  curl http://127.0.0.1:8080/api/v1/info");

    // 启动服务器
    axum::serve(listener, app).await?;

    Ok(())
}

/// 根路径处理函数
async fn root() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "message": "欢迎使用KVM隧道API服务器",
        "version": env!("CARGO_PKG_VERSION"),
        "description": "这是一个用于KVM虚拟机网络隧道的API服务器示例",
        "endpoints": [
            "/health - 健康检查",
            "/api/v1/info - 服务信息",
            "/api/v1/status - 服务状态"
        ]
    }))
}

/// 健康检查处理函数
async fn health_check() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "version": env!("CARGO_PKG_VERSION"),
        "uptime": "运行中"
    }))
}

/// 获取服务信息
async fn get_info() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "service": "KVM隧道API服务器",
        "version": env!("CARGO_PKG_VERSION"),
        "description": "用于管理KVM虚拟机网络隧道的RESTful API服务",
        "features": [
            "流媒体转发",
            "TCP/UDP隧道",
            "节点管理",
            "会话管理",
            "传输管理",
            "连接管理",
            "监控和告警"
        ],
        "protocols": [
            "HTTP/HTTPS",
            "WebSocket",
            "TCP",
            "UDP",
            "组播"
        ],
        "build_info": {
            "target": std::env::consts::ARCH,
            "os": std::env::consts::OS,
            "profile": if cfg!(debug_assertions) { "debug" } else { "release" }
        }
    }))
}

/// 获取服务状态
async fn get_status() -> Json<serde_json::Value> {
    use std::time::{SystemTime, UNIX_EPOCH};
    
    let uptime = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    Json(serde_json::json!({
        "status": "running",
        "uptime_seconds": uptime,
        "memory_usage": {
            "description": "内存使用情况（示例数据）",
            "used_mb": 128,
            "total_mb": 1024,
            "usage_percent": 12.5
        },
        "connections": {
            "active": 0,
            "total": 0,
            "max_allowed": 1000
        },
        "sessions": {
            "active": 0,
            "total": 0
        },
        "transports": {
            "active": 0,
            "total": 0
        },
        "tunnels": {
            "tcp": 0,
            "udp": 0,
            "multicast": 0
        },
        "last_updated": chrono::Utc::now().to_rfc3339()
    }))
}
