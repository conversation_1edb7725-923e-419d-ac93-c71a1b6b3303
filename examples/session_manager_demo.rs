// 会话管理器演示程序
// 展示会话管理器的主要功能

use std::sync::Arc;
use tokio::time::{sleep, Duration};
use kvm_tunnel::{
    config::{StorageConfig, MemoryStorageConfig, DiskStorageConfig},
    storage::{StorageManager, SessionStore},
    types::{
        SessionInfo, SessionType, SessionStatus, SessionConfig, SessionStats,
        TransportConfig, TransportType, AddressConfig, PortRange, Protocol,
        QosConfig,
    },
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 KVM隧道会话管理器演示");
    println!("========================");

    // 创建存储管理器
    let storage_config = StorageConfig {
        memory: MemoryStorageConfig {
            max_sessions: 1000,
            max_transports: 5000,
            cleanup_interval_seconds: 300,
        },
        disk: DiskStorageConfig {
            base_path: "/tmp/kvm_tunnel_demo".to_string(),
            max_log_size_mb: 10,
            log_retention_days: 7,
            sync_interval_seconds: 60,
        },
    };

    let storage_manager = Arc::new(StorageManager::new(storage_config).await?);
    println!("✅ 存储管理器创建成功");

    // 演示会话存储的基本操作
    demo_session_storage(&storage_manager).await?;

    // 演示会话生命周期管理
    demo_session_lifecycle(&storage_manager).await?;

    // 演示会话查询功能
    demo_session_queries(&storage_manager).await?;

    // 演示会话统计功能
    demo_session_statistics(&storage_manager).await?;

    println!("\n🎉 演示完成！");
    Ok(())
}

/// 演示会话存储的基本操作
async fn demo_session_storage(storage_manager: &Arc<StorageManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📦 演示会话存储操作");
    println!("------------------");

    // 创建一个发送端会话
    let tx_session = SessionInfo {
        session_id: "demo-tx-001".to_string(),
        session_type: SessionType::Transmitter,
        status: SessionStatus::Active,
        device_id: "kvm-device-001".to_string(),
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        config: SessionConfig {
            session_type: SessionType::Transmitter,
            device_id: "kvm-device-001".to_string(),
            transports: vec![
                TransportConfig {
                    transport_type: TransportType::MediaSoup,
                    addresses: vec![
                        AddressConfig {
                            ip: "*************".to_string(),
                            port: PortRange::Single(8080),
                            protocol: Protocol::TCP,
                        },
                    ],
                    security: None,
                    qos: None,
                },
            ],
            security: None,
            qos: Some(QosConfig {
                priority: 1,
                max_bandwidth_mbps: Some(100),
                max_latency_ms: Some(50),
            }),
        },
        stats: SessionStats {
            bytes_sent: 1024000,
            bytes_received: 512000,
            packets_sent: 1000,
            packets_received: 500,
            packet_loss_rate: 0.0,
            rtt_ms: 10,
            bandwidth_usage_mbps: 50.0,
            active_connections: 1,
        },
    };

    // 存储会话
    storage_manager.store_session(tx_session.session_id.clone(), tx_session.clone()).await?;
    println!("✅ 发送端会话已存储: {}", tx_session.session_id);

    // 创建一个接收端会话
    let rx_session = SessionInfo {
        session_id: "demo-rx-001".to_string(),
        session_type: SessionType::Receiver,
        status: SessionStatus::Active,
        device_id: "kvm-device-002".to_string(),
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        config: SessionConfig {
            session_type: SessionType::Receiver,
            device_id: "kvm-device-002".to_string(),
            transports: vec![
                TransportConfig {
                    transport_type: TransportType::MediaSoup,
                    addresses: vec![
                        AddressConfig {
                            ip: "*************".to_string(),
                            port: PortRange::Single(8081),
                            protocol: Protocol::TCP,
                        },
                    ],
                    security: None,
                    qos: None,
                },
            ],
            security: None,
            qos: Some(QosConfig {
                priority: 1,
                max_bandwidth_mbps: Some(100),
                max_latency_ms: Some(50),
            }),
        },
        stats: SessionStats::default(),
    };

    // 存储会话
    storage_manager.store_session(rx_session.session_id.clone(), rx_session.clone()).await?;
    println!("✅ 接收端会话已存储: {}", rx_session.session_id);

    // 验证存储
    let session_count = storage_manager.session_count().await;
    println!("📊 当前会话数量: {}", session_count);

    Ok(())
}

/// 演示会话生命周期管理
async fn demo_session_lifecycle(storage_manager: &Arc<StorageManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔄 演示会话生命周期管理");
    println!("----------------------");

    let session_id = "demo-lifecycle-001";

    // 创建会话
    let session = SessionInfo {
        session_id: session_id.to_string(),
        session_type: SessionType::Transmitter,
        status: SessionStatus::Initializing,
        device_id: "lifecycle-device".to_string(),
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        config: SessionConfig {
            session_type: SessionType::Transmitter,
            device_id: "lifecycle-device".to_string(),
            transports: vec![],
            security: None,
            qos: None,
        },
        stats: SessionStats::default(),
    };

    storage_manager.store_session(session_id.to_string(), session.clone()).await?;
    println!("✅ 会话创建: {} (状态: {:?})", session_id, session.status);

    // 模拟状态变化
    let states = vec![
        SessionStatus::Configuring,
        SessionStatus::Connecting,
        SessionStatus::Active,
        SessionStatus::Paused,
        SessionStatus::Active,
        SessionStatus::Terminating,
        SessionStatus::Terminated,
    ];

    for (i, status) in states.iter().enumerate() {
        sleep(Duration::from_millis(500)).await;
        
        storage_manager.update_session_status(&session_id.to_string(), status.clone()).await?;
        println!("🔄 状态更新 {}: {:?}", i + 1, status);
    }

    Ok(())
}

/// 演示会话查询功能
async fn demo_session_queries(storage_manager: &Arc<StorageManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔍 演示会话查询功能");
    println!("------------------");

    // 列出所有会话
    let all_sessions = storage_manager.list_sessions().await?;
    println!("📋 所有会话列表:");
    for session in &all_sessions {
        println!("  - {}: {} ({:?})", session.session_id, session.device_id, session.status);
    }

    // 查询特定会话
    if let Some(session) = storage_manager.get_session(&"demo-tx-001".to_string()).await? {
        println!("\n🔍 查询特定会话 (demo-tx-001):");
        println!("  设备ID: {}", session.device_id);
        println!("  类型: {:?}", session.session_type);
        println!("  状态: {:?}", session.status);
        println!("  创建时间: {}", session.created_at.format("%Y-%m-%d %H:%M:%S"));
        println!("  传输数量: {}", session.config.transports.len());
        println!("  发送字节: {}", session.stats.bytes_sent);
        println!("  接收字节: {}", session.stats.bytes_received);
    }

    // 检查会话存在性
    let exists = storage_manager.session_exists(&"demo-tx-001".to_string()).await;
    println!("\n✅ 会话 demo-tx-001 存在: {}", exists);

    let not_exists = storage_manager.session_exists(&"non-existent".to_string()).await;
    println!("❌ 会话 non-existent 存在: {}", not_exists);

    Ok(())
}

/// 演示会话统计功能
async fn demo_session_statistics(storage_manager: &Arc<StorageManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📊 演示会话统计功能");
    println!("------------------");

    let session_count = storage_manager.session_count().await;
    println!("📈 总会话数: {}", session_count);

    // 按类型统计
    let all_sessions = storage_manager.list_sessions().await?;
    let tx_count = all_sessions.iter().filter(|s| s.session_type == SessionType::Transmitter).count();
    let rx_count = all_sessions.iter().filter(|s| s.session_type == SessionType::Receiver).count();
    
    println!("📤 发送端会话: {}", tx_count);
    println!("📥 接收端会话: {}", rx_count);

    // 按状态统计
    let active_count = all_sessions.iter().filter(|s| s.status == SessionStatus::Active).count();
    let terminated_count = all_sessions.iter().filter(|s| s.status == SessionStatus::Terminated).count();
    
    println!("🟢 活跃会话: {}", active_count);
    println!("🔴 已终止会话: {}", terminated_count);

    // 计算总流量
    let total_bytes_sent: u64 = all_sessions.iter().map(|s| s.stats.bytes_sent).sum();
    let total_bytes_received: u64 = all_sessions.iter().map(|s| s.stats.bytes_received).sum();
    let total_packets_sent: u64 = all_sessions.iter().map(|s| s.stats.packets_sent).sum();
    let total_packets_received: u64 = all_sessions.iter().map(|s| s.stats.packets_received).sum();

    println!("📤 总发送流量: {} bytes ({:.2} MB)", total_bytes_sent, total_bytes_sent as f64 / 1024.0 / 1024.0);
    println!("📥 总接收流量: {} bytes ({:.2} MB)", total_bytes_received, total_bytes_received as f64 / 1024.0 / 1024.0);
    println!("📦 总发送包数: {}", total_packets_sent);
    println!("📦 总接收包数: {}", total_packets_received);

    Ok(())
}
