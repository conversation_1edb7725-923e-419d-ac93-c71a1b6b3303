// 连接管理器演示程序
// 展示连接管理器的主要功能

use std::sync::Arc;
use std::net::SocketAddr;
use tokio::time::{sleep, Duration};
use kvm_tunnel::{
    config::{StorageConfig, MemoryStorageConfig, DiskStorageConfig, NetworkConfig, TcpKeepaliveConfig, PortRangeConfig},
    storage::StorageManager,
    core::connection_manager::ConnectionManager,
    types::{
        ConnectionType, ConnectionStatus, TunnelType, TunnelStatus, NodeId,
    },
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 KVM隧道连接管理器演示");
    println!("========================");

    // 创建存储管理器
    let storage_config = StorageConfig {
        memory: MemoryStorageConfig {
            max_sessions: 1000,
            max_transports: 5000,
            cleanup_interval_seconds: 300,
        },
        disk: DiskStorageConfig {
            base_path: "/tmp/kvm_tunnel_connection_demo".to_string(),
            max_log_size_mb: 10,
            log_retention_days: 7,
            sync_interval_seconds: 60,
        },
    };

    let storage_manager = Arc::new(StorageManager::new(storage_config).await?);
    println!("✅ 存储管理器创建成功");

    // 创建网络配置
    let network_config = NetworkConfig {
        bind_address: "0.0.0.0".to_string(),
        api_port: 8080,
        websocket_port: 8081,
        max_connections: 1000,
        connection_timeout_seconds: 30,
        read_timeout_seconds: 10,
        write_timeout_seconds: 10,
        tcp_keepalive: TcpKeepaliveConfig {
            enabled: true,
            keepalive_time_seconds: 60,
            keepalive_interval_seconds: 10,
            keepalive_retries: 3,
        },
        port_ranges: PortRangeConfig {
            mediasoup_port_range: (10000, 20000),
            tcp_tunnel_port_range: (20000, 30000),
            udp_tunnel_port_range: (30000, 40000),
            multicast_port_range: (40000, 50000),
        },
    };

    // 创建连接管理器
    let connection_manager = Arc::new(ConnectionManager::new(
        network_config,
        storage_manager.clone(),
    ).await?);
    println!("✅ 连接管理器创建成功");

    // 启动连接管理器
    connection_manager.start().await?;
    println!("✅ 连接管理器启动成功");

    // 演示连接创建和管理
    demo_connection_creation(&connection_manager).await?;

    // 演示隧道创建和管理
    demo_tunnel_creation(&connection_manager).await?;

    // 演示连接生命周期管理
    demo_connection_lifecycle(&connection_manager).await?;

    // 演示连接查询功能
    demo_connection_queries(&connection_manager).await?;

    // 演示连接统计功能
    demo_connection_statistics(&connection_manager).await?;

    // 演示健康检查
    demo_health_check(&connection_manager).await?;

    // 停止连接管理器
    connection_manager.stop().await?;
    println!("✅ 连接管理器停止成功");

    println!("\n🎉 演示完成！");
    Ok(())
}

/// 演示连接创建和管理
async fn demo_connection_creation(connection_manager: &Arc<ConnectionManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📦 演示连接创建和管理");
    println!("--------------------");

    // 创建TCP连接
    let tcp_connection_id = connection_manager.create_inter_node_connection(
        "node-001".to_string(),
        "node-002".to_string(),
        ConnectionType::TcpConnection,
    ).await?;
    println!("✅ TCP连接创建成功: {}", tcp_connection_id);

    // 创建UDP连接
    let udp_connection_id = connection_manager.create_inter_node_connection(
        "node-001".to_string(),
        "node-003".to_string(),
        ConnectionType::UdpConnection,
    ).await?;
    println!("✅ UDP连接创建成功: {}", udp_connection_id);

    // 创建WebSocket连接
    let websocket_connection_id = connection_manager.create_inter_node_connection(
        "node-002".to_string(),
        "node-003".to_string(),
        ConnectionType::WebSocketConnection,
    ).await?;
    println!("✅ WebSocket连接创建成功: {}", websocket_connection_id);

    // 创建安全连接
    let secure_connection_id = connection_manager.create_inter_node_connection(
        "node-001".to_string(),
        "node-004".to_string(),
        ConnectionType::SecureConnection,
    ).await?;
    println!("✅ 安全连接创建成功: {}", secure_connection_id);

    // 验证连接数量
    let connection_count = connection_manager.connection_count().await;
    println!("📊 当前连接数量: {}", connection_count);

    Ok(())
}

/// 演示隧道创建和管理
async fn demo_tunnel_creation(connection_manager: &Arc<ConnectionManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔗 演示隧道创建和管理");
    println!("--------------------");

    // 创建TCP隧道
    let tcp_local_addr: SocketAddr = "127.0.0.1:8080".parse()?;
    let tcp_remote_addr: SocketAddr = "192.168.1.100:8080".parse()?;
    let tcp_tunnel_id = connection_manager.create_tcp_tunnel(
        tcp_local_addr,
        Some(tcp_remote_addr),
    ).await?;
    println!("✅ TCP隧道创建成功: {} ({}->{})", tcp_tunnel_id, tcp_local_addr, tcp_remote_addr);

    // 创建UDP隧道
    let udp_local_addr: SocketAddr = "127.0.0.1:8081".parse()?;
    let udp_remote_addr: SocketAddr = "192.168.1.100:8081".parse()?;
    let udp_tunnel_id = connection_manager.create_udp_tunnel(
        udp_local_addr,
        Some(udp_remote_addr),
    ).await?;
    println!("✅ UDP隧道创建成功: {} ({}->{})", udp_tunnel_id, udp_local_addr, udp_remote_addr);

    // 创建监听隧道（无远程地址）
    let listen_addr: SocketAddr = "0.0.0.0:9999".parse()?;
    let listen_tunnel_id = connection_manager.create_tcp_tunnel(
        listen_addr,
        None,
    ).await?;
    println!("✅ 监听隧道创建成功: {} (监听: {})", listen_tunnel_id, listen_addr);

    // 验证隧道数量
    let tunnel_count = connection_manager.tunnel_count().await;
    println!("📊 当前隧道数量: {}", tunnel_count);

    Ok(())
}

/// 演示连接生命周期管理
async fn demo_connection_lifecycle(connection_manager: &Arc<ConnectionManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔄 演示连接生命周期管理");
    println!("----------------------");

    // 获取第一个连接进行生命周期演示
    let connections = connection_manager.list_connections().await?;
    if let Some(connection) = connections.first() {
        let connection_id = &connection.connection_id;
        
        println!("🔄 演示连接: {} (类型: {:?})", connection_id, connection.connection_type);
        println!("   源节点: {} -> 目标节点: {}", connection.source_node, connection.target_node);

        // 等待一段时间让监控任务更新状态
        sleep(Duration::from_millis(500)).await;

        // 关闭连接
        connection_manager.close_connection(connection_id).await?;
        println!("⏹️ 连接关闭成功");
    }

    // 获取第一个隧道进行生命周期演示
    let tunnels = connection_manager.list_tunnels().await;
    if let Some(tunnel_id) = tunnels.first() {
        println!("\n🔄 演示隧道: {}", tunnel_id);

        // 获取隧道信息
        let tunnel_info = connection_manager.get_tunnel_info(tunnel_id).await?;
        println!("   类型: {:?}", tunnel_info.tunnel_type);
        println!("   本地地址: {}:{}", tunnel_info.local_address, tunnel_info.local_port);
        if let Some(remote_addr) = &tunnel_info.remote_address {
            if let Some(remote_port) = tunnel_info.remote_port {
                println!("   远程地址: {}:{}", remote_addr, remote_port);
            }
        }

        // 停止隧道
        connection_manager.stop_tunnel(tunnel_id).await?;
        println!("⏹️ 隧道停止成功");

        // 删除隧道
        connection_manager.remove_tunnel(tunnel_id).await?;
        println!("🗑️ 隧道删除成功");
    }

    Ok(())
}

/// 演示连接查询功能
async fn demo_connection_queries(connection_manager: &Arc<ConnectionManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔍 演示连接查询功能");
    println!("------------------");

    // 列出所有连接
    let all_connections = connection_manager.list_connections().await?;
    println!("📋 所有连接列表:");
    for connection in &all_connections {
        println!("  - {}: {:?} (状态: {:?})", 
            connection.connection_id, 
            connection.connection_type, 
            connection.status
        );
        println!("    {} -> {}", connection.source_node, connection.target_node);
    }

    // 按类型查询连接
    let tcp_connections = connection_manager.get_connections_by_type(ConnectionType::TcpConnection).await?;
    println!("\n🔗 TCP连接数量: {}", tcp_connections.len());

    let udp_connections = connection_manager.get_connections_by_type(ConnectionType::UdpConnection).await?;
    println!("📡 UDP连接数量: {}", udp_connections.len());

    let websocket_connections = connection_manager.get_connections_by_type(ConnectionType::WebSocketConnection).await?;
    println!("🌐 WebSocket连接数量: {}", websocket_connections.len());

    // 按状态查询连接
    let connected_connections = connection_manager.get_connections_by_status(ConnectionStatus::Connected).await?;
    println!("\n🟢 已连接数量: {}", connected_connections.len());

    let disconnected_connections = connection_manager.get_connections_by_status(ConnectionStatus::Disconnected).await?;
    println!("🔴 已断开连接数量: {}", disconnected_connections.len());

    // 查询特定连接详情
    if let Some(connection) = all_connections.first() {
        let connection_info = connection_manager.get_connection_info(&connection.connection_id).await?;
        println!("\n🔍 连接详情 ({}):", connection_info.connection_id);
        println!("  类型: {:?}", connection_info.connection_type);
        println!("  状态: {:?}", connection_info.status);
        println!("  源节点: {}", connection_info.source_node);
        println!("  目标节点: {}", connection_info.target_node);
        println!("  创建时间: {}", connection_info.created_at.format("%Y-%m-%d %H:%M:%S"));
        println!("  最后活跃: {}", connection_info.last_active.format("%Y-%m-%d %H:%M:%S"));
    }

    // 列出所有隧道
    let all_tunnels = connection_manager.list_tunnels().await;
    println!("\n🔗 隧道列表:");
    for tunnel_id in &all_tunnels {
        if let Ok(tunnel_info) = connection_manager.get_tunnel_info(tunnel_id).await {
            println!("  - {}: {:?} (状态: {:?})", 
                tunnel_info.tunnel_id, 
                tunnel_info.tunnel_type, 
                tunnel_info.status
            );
            println!("    本地: {}:{}", tunnel_info.local_address, tunnel_info.local_port);
        }
    }

    Ok(())
}

/// 演示连接统计功能
async fn demo_connection_statistics(connection_manager: &Arc<ConnectionManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📊 演示连接统计功能");
    println!("------------------");

    let connection_count = connection_manager.connection_count().await;
    let tunnel_count = connection_manager.tunnel_count().await;
    println!("📈 总连接数: {}", connection_count);
    println!("📈 总隧道数: {}", tunnel_count);

    // 按类型统计连接
    let all_connections = connection_manager.list_connections().await?;
    
    let mut type_counts = std::collections::HashMap::new();
    for connection in &all_connections {
        *type_counts.entry(connection.connection_type.clone()).or_insert(0) += 1;
    }

    println!("\n📊 按类型统计连接:");
    for (connection_type, count) in type_counts {
        println!("  {:?}: {}", connection_type, count);
    }

    // 按状态统计连接
    let mut status_counts = std::collections::HashMap::new();
    for connection in &all_connections {
        *status_counts.entry(connection.status).or_insert(0) += 1;
    }

    println!("\n📊 按状态统计连接:");
    for (status, count) in status_counts {
        println!("  {:?}: {}", status, count);
    }

    // 获取连接统计信息
    if let Some(connection) = all_connections.first() {
        match connection_manager.get_connection_stats(&connection.connection_id).await {
            Ok(stats) => {
                println!("\n📊 连接统计 ({}):", connection.connection_id);
                println!("  传输字节: {}", stats.bytes_transferred);
                println!("  发送包数: {}", stats.packets_sent);
                println!("  丢包率: {:.2}%", stats.packet_loss_rate * 100.0);
                println!("  延迟: {} ms", stats.latency_ms);
                println!("  带宽: {:.2} Mbps", stats.bandwidth_mbps);
            }
            Err(e) => {
                println!("❌ 获取连接统计失败: {}", e);
            }
        }
    }

    Ok(())
}

/// 演示健康检查
async fn demo_health_check(connection_manager: &Arc<ConnectionManager>) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🏥 演示健康检查");
    println!("--------------");

    let is_healthy = connection_manager.health_check().await;
    if is_healthy {
        println!("✅ 连接管理器健康状态: 良好");
    } else {
        println!("❌ 连接管理器健康状态: 异常");
    }

    println!("📊 健康检查详情:");
    println!("  - 检查所有活跃连接的健康状态");
    println!("  - 如果80%以上连接健康，则整体健康");
    println!("  - 如果没有连接，则认为健康");

    Ok(())
}
