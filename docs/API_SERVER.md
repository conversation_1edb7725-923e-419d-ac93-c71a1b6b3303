# KVM隧道API服务器

## 概述

KVM隧道API服务器是一个基于Rust和Axum框架构建的RESTful API服务，用于管理KVM虚拟机之间的网络隧道和流媒体转发。

## 功能特性

### 核心功能
- **流媒体转发**: 支持实时音视频流转发
- **TCP/UDP隧道**: 提供可靠的网络隧道服务
- **节点管理**: 管理分布式节点网络
- **会话管理**: 处理客户端会话生命周期
- **传输管理**: 管理各种传输协议
- **连接管理**: 处理节点间连接
- **监控告警**: 实时监控和告警系统

### 技术特性
- **异步处理**: 基于Tokio的高性能异步运行时
- **安全认证**: 支持证书认证和访问控制
- **速率限制**: 防止API滥用
- **健康检查**: 提供服务健康状态监控
- **中间件支持**: 支持认证、日志、CORS等中间件
- **错误处理**: 完善的错误处理和响应机制

## API端点

### 基础端点
- `GET /health` - 健康检查
- `GET /api/v1/metrics` - 获取监控指标
- `GET /api/v1/alerts` - 获取告警信息

### 节点管理
- `GET /api/v1/node/info` - 获取节点信息
- `GET /api/v1/node/status` - 获取节点状态
- `GET /api/v1/node/stats` - 获取节点统计
- `POST /api/v1/node/register` - 注册节点
- `POST /api/v1/node/heartbeat` - 发送心跳

### 会话管理
- `GET /api/v1/sessions` - 列出所有会话
- `POST /api/v1/sessions` - 创建新会话
- `GET /api/v1/sessions/{id}` - 获取会话详情
- `DELETE /api/v1/sessions/{id}` - 删除会话
- `GET /api/v1/sessions/{id}/stats` - 获取会话统计

### 传输管理
- `GET /api/v1/transports` - 列出所有传输
- `POST /api/v1/transports` - 创建新传输
- `GET /api/v1/transports/{id}` - 获取传输详情
- `DELETE /api/v1/transports/{id}` - 删除传输
- `POST /api/v1/transports/{id}/start` - 启动传输
- `POST /api/v1/transports/{id}/stop` - 停止传输
- `GET /api/v1/transports/{id}/stats` - 获取传输统计

### 连接管理
- `GET /api/v1/connections` - 列出所有连接
- `POST /api/v1/connections` - 创建新连接
- `GET /api/v1/connections/{id}` - 获取连接详情
- `DELETE /api/v1/connections/{id}` - 删除连接
- `GET /api/v1/connections/{id}/stats` - 获取连接统计

### 隧道管理
- `GET /api/v1/tunnels` - 列出所有隧道
- `POST /api/v1/tunnels` - 创建新隧道
- `GET /api/v1/tunnels/{id}` - 获取隧道详情
- `DELETE /api/v1/tunnels/{id}` - 删除隧道
- `GET /api/v1/tunnels/{id}/stats` - 获取隧道统计

### 配置管理
- `GET /api/v1/config` - 获取配置
- `PUT /api/v1/config` - 更新配置

## 使用示例

### 启动API服务器
```bash
# 运行示例服务器
cargo run --example api_server_example

# 或者运行完整的KVM隧道服务
cargo run
```

### 基本API调用
```bash
# 健康检查
curl http://localhost:8080/health

# 获取节点信息
curl http://localhost:8080/api/v1/node/info

# 获取会话列表
curl http://localhost:8080/api/v1/sessions

# 创建新会话
curl -X POST http://localhost:8080/api/v1/sessions \
  -H "Content-Type: application/json" \
  -d '{"config": {"session_type": "Receiver", "timeout_seconds": 300}}'
```

### 响应格式
所有API响应都使用JSON格式：

```json
{
  "status": "success",
  "data": {...},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

错误响应格式：
```json
{
  "error": "错误描述",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 配置

### 网络配置
```toml
[network]
bind_address = "0.0.0.0"
api_port = 8080
websocket_port = 8081
max_connections = 1000
connection_timeout_seconds = 30
```

### 安全配置
```toml
[security]
node_certificate_path = "/path/to/node.pem"
node_private_key_path = "/path/to/node.key"
ca_certificate_path = "/path/to/ca.pem"
```

## 开发和测试

### 运行测试
```bash
# 运行所有测试
cargo test

# 运行API集成测试
cargo test test_simple_health_check
```

### 开发模式
```bash
# 启用详细日志
RUST_LOG=debug cargo run

# 启用追踪
RUST_LOG=trace cargo run
```

## 部署

### Docker部署
```dockerfile
FROM rust:1.70 as builder
WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bullseye-slim
COPY --from=builder /app/target/release/kvm_tunnel /usr/local/bin/
EXPOSE 8080
CMD ["kvm_tunnel"]
```

### 系统服务
```ini
[Unit]
Description=KVM Tunnel API Server
After=network.target

[Service]
Type=simple
User=kvm-tunnel
ExecStart=/usr/local/bin/kvm_tunnel
Restart=always

[Install]
WantedBy=multi-user.target
```

## 监控和日志

### 日志级别
- `ERROR`: 错误信息
- `WARN`: 警告信息
- `INFO`: 一般信息
- `DEBUG`: 调试信息
- `TRACE`: 详细追踪信息

### 监控指标
- 请求计数和响应时间
- 活跃连接数
- 内存和CPU使用率
- 错误率和成功率

## 故障排除

### 常见问题
1. **端口被占用**: 检查端口是否被其他进程使用
2. **证书错误**: 确认证书路径和权限正确
3. **连接超时**: 检查网络配置和防火墙设置
4. **内存不足**: 调整最大连接数和缓存大小

### 日志分析
```bash
# 查看错误日志
grep "ERROR" /var/log/kvm-tunnel.log

# 查看API调用统计
grep "API" /var/log/kvm-tunnel.log | grep "200"
```
