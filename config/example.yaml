# KVM隧道转发节点示例配置文件
# 这是一个完整的配置示例，展示了所有可配置的选项

# 节点基础配置
node:
  node_id: "forwarder-node-001"
  node_type: "Forwarder"
  listen_ip: "0.0.0.0"
  listen_port: 8080
  region: "beijing"
  capabilities:
    max_connections: 1000
    max_bandwidth_mbps: 1000
    supported_codecs:
      - "H264"
      - "VP8"
      - "VP9"
      - "AV1"
    supported_protocols:
      - "WebRTC"
      - "TCP"
      - "UDP"
      - "Multicast"
    supports_mediasoup: true
    supports_tcp_tunnel: true
    supports_udp_tunnel: true
    supports_multicast: true
  management_server_url: "https://management.kvm-tunnel.com"
  heartbeat_interval_seconds: 30
  session_timeout_seconds: 300

# 网络配置
network:
  bind_address: "0.0.0.0"
  api_port: 8080
  websocket_port: 8081
  max_connections: 1000
  connection_timeout_seconds: 30
  read_timeout_seconds: 30
  write_timeout_seconds: 30
  tcp_keepalive:
    enabled: true
    keepalive_time_seconds: 600
    keepalive_interval_seconds: 60
    keepalive_retries: 3
  port_ranges:
    mediasoup_port_range: [10000, 20000]
    tcp_tunnel_port_range: [20001, 30000]
    udp_tunnel_port_range: [30001, 40000]
    multicast_port_range: [40001, 50000]

# MediaSoup配置
mediasoup:
  worker_count: 4
  max_connections_per_worker: 250
  rtc_port_range: [10000, 20000]
  log_level: "warn"
  log_tags:
    - "info"
    - "ice"
    - "dtls"
    - "rtp"
    - "srtp"
  dtls_cert_file: null
  dtls_key_file: null

# 安全配置
security:
  node_certificate_path: "/etc/ssl/certs/forwarder-node.pem"
  node_private_key_path: "/etc/ssl/private/forwarder-node.key"
  ca_certificate_path: "/etc/ssl/certs/ca.pem"
  certificate_rotation_days: 90
  certificate_check_interval_hours: 1
  trusted_servers: []
  api_permissions:
    management_only_apis:
      - "POST /api/v1/sessions/transmitter"
      - "DELETE /api/v1/sessions/transmitter/*"
      - "POST /api/v1/sessions/receiver"
      - "DELETE /api/v1/sessions/receiver/*"
      - "POST /api/v1/config/update"
    inter_node_only_apis:
      - "POST /api/v1/inter-node/access-request"
      - "GET /api/v1/inter-node/connections/*"
      - "POST /api/v1/inter-node/pipe-transport"
    both_callable_apis:
      - "GET /api/v1/status"
      - "GET /api/v1/stats"
      - "GET /health"
  rate_limits:
    enabled: true
    max_requests_per_minute: 100
    burst_size: 10
    window_size_seconds: 60

# 日志配置
logging:
  level: "info"
  format: "Json"
  targets:
    - "Stdout"
    - "File"
  file:
    path: "/var/log/kvm_tunnel/forwarder.log"
    max_size_mb: 100
    max_files: 10
    compress: true
  structured_fields:
    - "timestamp"
    - "level"
    - "target"
    - "message"
    - "node_id"
    - "session_id"

# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  metrics_interval_seconds: 15
  health_check_port: 8082
  alerts:
    enabled: true
    webhook_url: "https://alerts.kvm-tunnel.com/webhook"
    thresholds:
      cpu_usage_threshold: 80.0
      memory_usage_threshold: 85.0
      disk_usage_threshold: 90.0
      error_rate_threshold: 5.0

# 存储配置
storage:
  memory:
    max_sessions: 10000
    max_transports: 50000
    cleanup_interval_seconds: 300
  disk:
    base_path: "/var/lib/kvm_tunnel"
    max_log_size_mb: 100
    log_retention_days: 30
    sync_interval_seconds: 60
