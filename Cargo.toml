[package]
name = "kvm_tunnel"
version = "0.1.0"
edition = "2021"

[dependencies]
# 异步运行时
tokio = { version = "1.32.0", features = ["full"] }

# Web框架
axum = { version = "0.7.2", features = ["ws", "multipart"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }

# 序列化
serde = { version = "1.0.188", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# 日志系统
tracing = "0.1.37"
tracing-subscriber = { version = "0.3", features = ["json", "env-filter"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 异步工具
futures = "0.3"
async-trait = "0.1"

# 网络和加密
rustls = "0.21"
rustls-pemfile = "1.0"
tokio-rustls = "0.24"

# 数据结构
dashmap = "5.5"
arc-swap = "1.6"
uuid = { version = "1.0", features = ["v4", "serde"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 配置管理
config = "0.13"

# 监控指标
prometheus = "0.13"

# HTTP客户端
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }

# 网络工具
socket2 = "0.5"

[dev-dependencies]
tokio-test = "0.4"
criterion = "0.5"
mockall = "0.11"
