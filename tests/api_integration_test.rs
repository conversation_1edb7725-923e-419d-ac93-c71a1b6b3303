use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
    routing::get,
    Router,
};
use serde_json;
use tokio::net::TcpListener;
use reqwest;

// 简化的健康检查测试
#[tokio::test]
async fn test_simple_health_check() {
    // 创建简单的健康检查路由
    let app = Router::new()
        .route("/health", get(health_check));

    // 绑定到随机端口
    let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
    let addr = listener.local_addr().unwrap();

    // 启动服务器
    let server_handle = tokio::spawn(async move {
        axum::serve(listener, app).await.unwrap();
    });

    // 等待服务器启动
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 测试健康检查端点
    let client = reqwest::Client::new();
    let url = format!("http://{}/health", addr);

    let response = client.get(&url).send().await.unwrap();
    assert_eq!(response.status(), 200);

    let body: serde_json::Value = response.json().await.unwrap();
    assert_eq!(body["status"], "healthy");
    assert!(body["timestamp"].is_string());
    assert!(body["version"].is_string());

    // 停止服务器
    server_handle.abort();
}

// 健康检查处理函数
async fn health_check() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "version": env!("CARGO_PKG_VERSION")
    }))
}

#[tokio::test]
async fn test_api_server_node_info() {
    // 创建网络配置
    let config = NetworkConfig {
        bind_address: "127.0.0.1".to_string(),
        api_port: 0,
        websocket_port: 0,
        max_connections: 1000,
        connection_timeout_seconds: 30,
        read_timeout_seconds: 30,
        write_timeout_seconds: 30,
        tcp_keepalive: kvm_tunnel::config::TcpKeepaliveConfig {
            enabled: true,
            keepalive_time_seconds: 60,
            keepalive_interval_seconds: 10,
            keepalive_retries: 3,
        },
        port_ranges: kvm_tunnel::config::PortRangeConfig {
            mediasoup_port_range: (9000, 10000),
            tcp_tunnel_port_range: (10000, 11000),
            udp_tunnel_port_range: (11000, 12000),
            multicast_port_range: (12000, 13000),
        },
    };

    // 绑定到随机端口
    let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
    let addr = listener.local_addr().unwrap();
    
    // 创建API服务器
    let api_server = ApiServer::new_basic(listener, config).await.unwrap();
    
    // 启动服务器
    let server_handle = tokio::spawn(async move {
        api_server.start().await.unwrap();
    });

    // 等待服务器启动
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 测试节点信息端点
    let client = reqwest::Client::new();
    let url = format!("http://{}/api/v1/node/info", addr);
    
    let response = client.get(&url).send().await.unwrap();
    assert_eq!(response.status(), 200);
    
    let body: serde_json::Value = response.json().await.unwrap();
    assert!(body["node_id"].is_string());
    assert!(body["node_type"].is_string());
    assert!(body["capabilities"].is_object());

    // 停止服务器
    server_handle.abort();
}

#[tokio::test]
async fn test_api_server_sessions_list() {
    // 创建网络配置
    let config = NetworkConfig {
        bind_address: "127.0.0.1".to_string(),
        api_port: 0,
        websocket_port: 0,
        max_connections: 1000,
        connection_timeout_seconds: 30,
        read_timeout_seconds: 30,
        write_timeout_seconds: 30,
        tcp_keepalive: kvm_tunnel::config::TcpKeepaliveConfig {
            enabled: true,
            keepalive_time_seconds: 60,
            keepalive_interval_seconds: 10,
            keepalive_retries: 3,
        },
        port_ranges: kvm_tunnel::config::PortRangeConfig {
            mediasoup_port_range: (9000, 10000),
            tcp_tunnel_port_range: (10000, 11000),
            udp_tunnel_port_range: (11000, 12000),
            multicast_port_range: (12000, 13000),
        },
    };

    // 绑定到随机端口
    let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
    let addr = listener.local_addr().unwrap();
    
    // 创建API服务器
    let api_server = ApiServer::new_basic(listener, config).await.unwrap();
    
    // 启动服务器
    let server_handle = tokio::spawn(async move {
        api_server.start().await.unwrap();
    });

    // 等待服务器启动
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 测试会话列表端点
    let client = reqwest::Client::new();
    let url = format!("http://{}/api/v1/sessions", addr);
    
    let response = client.get(&url).send().await.unwrap();
    assert_eq!(response.status(), 200);
    
    let body: serde_json::Value = response.json().await.unwrap();
    assert!(body.is_array());

    // 停止服务器
    server_handle.abort();
}
