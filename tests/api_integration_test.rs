use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
    routing::get,
    Router,
};
use serde_json;
use tokio::net::TcpListener;
use reqwest;

// 简化的健康检查测试
#[tokio::test]
async fn test_simple_health_check() {
    // 创建简单的健康检查路由
    let app = Router::new()
        .route("/health", get(health_check));

    // 绑定到随机端口
    let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
    let addr = listener.local_addr().unwrap();

    // 启动服务器
    let server_handle = tokio::spawn(async move {
        axum::serve(listener, app).await.unwrap();
    });

    // 等待服务器启动
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 测试健康检查端点
    let client = reqwest::Client::new();
    let url = format!("http://{}/health", addr);

    let response = client.get(&url).send().await.unwrap();
    assert_eq!(response.status(), 200);

    let body: serde_json::Value = response.json().await.unwrap();
    assert_eq!(body["status"], "healthy");
    assert!(body["timestamp"].is_string());
    assert!(body["version"].is_string());

    // 停止服务器
    server_handle.abort();
}

// 健康检查处理函数
async fn health_check() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "version": env!("CARGO_PKG_VERSION")
    }))
}


