# KVM隧道转发节点

这是一个基于Rust实现的KVM隧道转发节点，用于提供高性能的流媒体转发和TCP/UDP隧道功能。

## 功能特性

### 核心功能
- **流媒体转发**: 基于MediaSoup的高性能流媒体转发
- **TCP/UDP隧道**: 支持TCP和UDP协议的隧道转发
- **组播支持**: 支持组播数据的转发和管理
- **节点间通信**: 支持多节点之间的数据传输

### 安全特性
- **mTLS双向认证**: 使用证书进行节点间的安全通信
- **访问控制**: 基于角色的API访问控制
- **速率限制**: 防止API滥用的速率限制机制
- **安全审计**: 完整的安全事件记录和审计

### 监控和管理
- **Prometheus指标**: 完整的性能和状态指标
- **健康检查**: 实时的系统健康状态监控
- **结构化日志**: 便于分析的JSON格式日志
- **告警系统**: 基于阈值的自动告警

## 快速开始

### 编译项目

```bash
# 克隆项目
git clone <repository-url>
cd kvm_tunnel

# 编译项目
cargo build --release

# 运行测试
cargo test
```

### 配置文件

复制示例配置文件并根据需要修改：

```bash
cp config/example.yaml /etc/kvm_tunnel/config.yaml
```

主要配置项：
- `node.node_id`: 节点唯一标识
- `node.management_server_url`: 管理服务器地址
- `network.api_port`: API服务端口
- `security.node_certificate_path`: 节点证书路径

### 运行节点

```bash
# 设置配置文件路径
export KVM_TUNNEL_CONFIG=/etc/kvm_tunnel/config.yaml

# 运行转发节点
./target/release/kvm_tunnel
```

## 架构设计

### 模块结构

```
src/
├── config/          # 配置管理
├── core/            # 核心组件
│   ├── node_manager.rs      # 节点管理
│   ├── session_manager.rs   # 会话管理
│   ├── transport_manager.rs # 传输管理
│   └── connection_manager.rs # 连接管理
├── network/         # 网络通信
│   ├── api.rs       # REST API
│   ├── websocket.rs # WebSocket服务
│   ├── tunnel.rs    # TCP/UDP隧道
│   └── multicast.rs # 组播管理
├── security/        # 安全管理
│   ├── certificate.rs    # 证书管理
│   ├── access_control.rs # 访问控制
│   ├── audit.rs          # 安全审计
│   └── rate_limit.rs     # 速率限制
├── storage/         # 存储管理
│   ├── memory.rs    # 内存存储
│   └── persistent.rs # 持久化存储
├── monitoring/      # 监控系统
│   ├── metrics.rs   # 指标收集
│   └── alerts.rs    # 告警管理
└── types.rs         # 数据类型定义
```

### 核心组件

1. **节点管理器**: 负责节点的生命周期管理和与管理服务器的通信
2. **会话管理器**: 管理KVM设备的会话连接
3. **传输管理器**: 处理MediaSoup和其他传输协议
4. **连接管理器**: 管理TCP/UDP隧道连接
5. **安全管理器**: 提供认证、授权和审计功能
6. **存储管理器**: 管理内存和持久化数据
7. **监控管理器**: 收集指标和处理告警

## API接口

### 健康检查
```
GET /health
```

### 节点状态
```
GET /api/v1/status
```

### 节点统计
```
GET /api/v1/stats
```

### Prometheus指标
```
GET /metrics
```

## 配置说明

### 节点配置
- `node_id`: 节点唯一标识符
- `node_type`: 节点类型（Forwarder/Management）
- `capabilities`: 节点能力描述

### 网络配置
- `api_port`: REST API端口
- `websocket_port`: WebSocket端口
- `port_ranges`: 各种服务的端口范围

### 安全配置
- `node_certificate_path`: 节点证书文件路径
- `api_permissions`: API权限配置
- `rate_limits`: 速率限制配置

### 监控配置
- `metrics_port`: Prometheus指标端口
- `alerts`: 告警配置

## 开发指南

### 添加新功能

1. 在相应模块中添加功能代码
2. 更新数据类型定义（如需要）
3. 添加配置选项（如需要）
4. 编写单元测试
5. 更新文档

### 测试

```bash
# 运行所有测试
cargo test

# 运行单元测试
cargo test --lib

# 运行集成测试
cargo test --test integration_tests

# 运行特定测试
cargo test test_name
```

### 代码风格

项目使用标准的Rust代码风格：

```bash
# 格式化代码
cargo fmt

# 检查代码
cargo clippy
```

## 部署

### 系统要求
- Linux操作系统
- Rust 1.70+
- 足够的网络带宽
- SSL证书（用于mTLS）

### 生产部署
1. 编译release版本
2. 配置SSL证书
3. 设置系统服务
4. 配置防火墙规则
5. 设置监控和日志收集

## 故障排除

### 常见问题

1. **证书错误**: 检查证书路径和有效期
2. **端口冲突**: 确保配置的端口未被占用
3. **连接超时**: 检查网络连接和防火墙设置
4. **内存不足**: 调整内存配置参数

### 日志分析

日志文件位置：`/var/log/kvm_tunnel/forwarder.log`

重要日志关键词：
- `ERROR`: 错误信息
- `WARN`: 警告信息
- `node_manager`: 节点管理相关
- `session_manager`: 会话管理相关

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
