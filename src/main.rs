// 转发节点主程序
// 负责启动和管理转发节点的各个组件

mod config;
mod core;
mod error;
mod network;
mod security;
mod storage;
mod monitoring;
mod types;

use anyhow::Result;
use tracing::{info, error};
use std::sync::Arc;

use crate::config::ForwarderConfig;
use crate::core::ForwarderNode;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    init_logging()?;

    info!("启动KVM隧道转发节点");

    // 加载配置
    let config = ForwarderConfig::load().await?;
    info!("配置加载完成: {}", config.node.node_id);

    // 创建转发节点实例
    let forwarder = Arc::new(ForwarderNode::new(config).await?);

    // 启动转发节点
    match forwarder.start().await {
        Ok(_) => {
            info!("转发节点启动成功");

            // 等待关闭信号
            tokio::signal::ctrl_c().await?;
            info!("收到关闭信号，正在停止转发节点");

            // 优雅关闭
            forwarder.stop().await?;
            info!("转发节点已停止");
        }
        Err(e) => {
            error!("转发节点启动失败: {}", e);
            return Err(e);
        }
    }

    Ok(())
}

/// 初始化日志系统
fn init_logging() -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "kvm_tunnel=info".into())
        )
        .with(tracing_subscriber::fmt::layer().json())
        .init();

    Ok(())
}
