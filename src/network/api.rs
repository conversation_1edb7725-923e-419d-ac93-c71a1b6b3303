// API服务器实现
// 提供RESTful API接口，支持节点管理、会话管理、监控等功能

use std::sync::Arc;
use std::net::SocketAddr;
use std::collections::HashMap;
use tokio::sync::RwLock;
use tokio::net::TcpListener;
use axum::{
    extract::{Path, Query, State},
    http::{StatusCode, HeaderMap, Method},
    response::{Json, Response},
    routing::{get, post, delete, put},
    Router,
    middleware::{self, Next},
    body::Body,
};
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
};
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error, debug};

use crate::config::NetworkConfig;
use crate::error::{NetworkError, NetworkResult, SecurityError};
use crate::security::SecurityManager;
use crate::core::{
    node_manager::NodeManager,
    session_manager::SessionManager,
    transport_manager::TransportManager,
    connection_manager::ConnectionManager,
};
use crate::storage::StorageManager;
use crate::monitoring::MonitoringManager;
use crate::types::{
    NodeId, SessionId, TransportId, ConnectionId, TunnelId,
    SessionConfig, TransportConfig, ConnectionType,
    NodeInfo, SessionInfo, TransportInfo, InterNodeConnection,
};

/// API服务器
pub struct ApiServer {
    /// 监听器
    listener: TcpListener,

    /// 网络配置
    config: NetworkConfig,

    /// 安全管理器
    security_manager: Arc<SecurityManager>,

    /// 节点管理器
    node_manager: Arc<NodeManager>,

    /// 会话管理器
    session_manager: Arc<SessionManager>,

    /// 传输管理器
    transport_manager: Arc<TransportManager>,

    /// 连接管理器
    connection_manager: Arc<ConnectionManager>,

    /// 存储管理器
    storage_manager: Arc<StorageManager>,

    /// 监控管理器
    monitoring_manager: Arc<MonitoringManager>,

    /// API统计信息
    stats: Arc<RwLock<ApiStats>>,

    /// 运行状态
    running: Arc<RwLock<bool>>,

    /// 服务器任务句柄
    server_handle: Arc<RwLock<Option<tokio::task::JoinHandle<()>>>>,
}

/// API统计信息
#[derive(Debug, Clone, Default, Serialize)]
pub struct ApiStats {
    /// 总连接数
    pub total_connections: u64,
    /// 活跃连接数
    pub active_connections: u32,
    /// 发送字节数
    pub bytes_sent: u64,
    /// 接收字节数
    pub bytes_received: u64,
    /// 请求总数
    pub total_requests: u64,
    /// 成功请求数
    pub successful_requests: u64,
    /// 失败请求数
    pub failed_requests: u64,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
}

/// API应用状态
#[derive(Clone)]
pub struct AppState {
    /// 安全管理器
    pub security_manager: Arc<SecurityManager>,
    /// 节点管理器
    pub node_manager: Arc<NodeManager>,
    /// 会话管理器
    pub session_manager: Arc<SessionManager>,
    /// 传输管理器
    pub transport_manager: Arc<TransportManager>,
    /// 连接管理器
    pub connection_manager: Arc<ConnectionManager>,
    /// 存储管理器
    pub storage_manager: Arc<StorageManager>,
    /// 监控管理器
    pub monitoring_manager: Arc<MonitoringManager>,
    /// API统计信息
    pub stats: Arc<RwLock<ApiStats>>,
}

impl ApiServer {
    /// 创建新的API服务器（完整版本）
    pub async fn new(
        listener: TcpListener,
        config: NetworkConfig,
        security_manager: Arc<SecurityManager>,
        node_manager: Arc<NodeManager>,
        session_manager: Arc<SessionManager>,
        transport_manager: Arc<TransportManager>,
        connection_manager: Arc<ConnectionManager>,
        storage_manager: Arc<StorageManager>,
        monitoring_manager: Arc<MonitoringManager>,
    ) -> NetworkResult<Self> {
        Ok(Self {
            listener,
            config,
            security_manager,
            node_manager,
            session_manager,
            transport_manager,
            connection_manager,
            storage_manager,
            monitoring_manager,
            stats: Arc::new(RwLock::new(ApiStats::default())),
            running: Arc::new(RwLock::new(false)),
            server_handle: Arc::new(RwLock::new(None)),
        })
    }

    /// 创建简化的API服务器（仅用于基础功能）
    pub async fn new_basic(
        listener: TcpListener,
        config: NetworkConfig,
    ) -> NetworkResult<Self> {
        // 创建占位符管理器，使用简化的实现
        use crate::config::{SecurityConfig, StorageConfig, MemoryStorageConfig, DiskStorageConfig, MediaSoupConfig, MonitoringConfig, AlertConfig};
        use crate::security::SecurityManager;
        use crate::storage::StorageManager;
        use crate::monitoring::MonitoringManager;
        use crate::core::{
            node_manager::NodeManager,
            session_manager::SessionManager,
            transport_manager::TransportManager,
            connection_manager::ConnectionManager,
        };

        // 创建基础配置
        let security_config = SecurityConfig {
            node_certificate_path: "/tmp/node.pem".to_string(),
            node_private_key_path: "/tmp/node.key".to_string(),
            ca_certificate_path: "/tmp/ca.pem".to_string(),
            certificate_rotation_days: 90,
            certificate_check_interval_hours: 24,
            trusted_servers: vec![],
            api_permissions: crate::config::ApiPermissionConfig {
                management_only_apis: vec![],
                inter_node_only_apis: vec![],
                both_callable_apis: vec!["GET /health".to_string()],
            },
            rate_limits: crate::config::RateLimitConfig {
                enabled: false,
                max_requests_per_minute: 100,
                burst_size: 10,
                window_size_seconds: 60,
            },
        };

        let storage_config = StorageConfig {
            memory: MemoryStorageConfig {
                max_sessions: 1000,
                max_transports: 5000,
                cleanup_interval_seconds: 300,
            },
            disk: DiskStorageConfig {
                base_path: "/tmp/kvm_tunnel_api".to_string(),
                max_log_size_mb: 10,
                log_retention_days: 7,
                sync_interval_seconds: 60,
            },
        };

        let mediasoup_config = MediaSoupConfig {
            worker_count: 1,
            max_connections_per_worker: 100,
            rtc_port_range: (10000, 20000),
            log_level: "warn".to_string(),
            log_tags: vec!["info".to_string()],
            dtls_cert_file: None,
            dtls_key_file: None,
        };

        let monitoring_config = MonitoringConfig {
            enabled: true,
            metrics_port: 9090,
            metrics_interval_seconds: 30,
            health_check_port: 9091,
            alerts: crate::config::AlertConfig {
                enabled: false,
                webhook_url: None,
                thresholds: crate::config::AlertThresholds {
                    cpu_usage_threshold: 80.0,
                    memory_usage_threshold: 80.0,
                    disk_usage_threshold: 90.0,
                    error_rate_threshold: 5.0,
                },
            },
        };

        // 创建管理器实例
        let security_manager = Arc::new(SecurityManager::new(security_config).await
            .map_err(|_e| NetworkError::PortBindFailed { port: config.api_port })?);
        let storage_manager = Arc::new(StorageManager::new(storage_config).await
            .map_err(|_e| NetworkError::PortBindFailed { port: config.api_port })?);
        let monitoring_manager = Arc::new(MonitoringManager::new(monitoring_config).await
            .map_err(|_e| NetworkError::PortBindFailed { port: config.api_port })?);
        let node_manager = Arc::new(NodeManager::new(
            crate::config::NodeConfig {
                node_id: "api-server-node".to_string(),
                node_type: crate::types::NodeType::Forwarder,
                listen_ip: "0.0.0.0".to_string(),
                listen_port: 8080,
                region: "default".to_string(),
                capabilities: crate::types::NodeCapabilities {
                    max_connections: 1000,
                    max_bandwidth_mbps: 100,
                    supported_codecs: vec!["h264".to_string(), "vp8".to_string()],
                    supported_protocols: vec!["tcp".to_string(), "udp".to_string()],
                    supports_mediasoup: true,
                    supports_tcp_tunnel: true,
                    supports_udp_tunnel: true,
                    supports_multicast: false,
                },
                management_server_url: "http://localhost:8080".to_string(),
                heartbeat_interval_seconds: 30,
                session_timeout_seconds: 300,
            },
            Arc::clone(&storage_manager),
            Arc::clone(&security_manager),
        ).await.map_err(|_e| NetworkError::PortBindFailed { port: config.api_port })?);
        let session_manager = Arc::new(SessionManager::new(
            Arc::clone(&storage_manager),
            Arc::clone(&security_manager),
        ).await.map_err(|_e| NetworkError::PortBindFailed { port: config.api_port })?);
        let transport_manager = Arc::new(TransportManager::new(
            mediasoup_config,
            Arc::clone(&storage_manager),
        ).await.map_err(|_e| NetworkError::PortBindFailed { port: config.api_port })?);
        let connection_manager = Arc::new(ConnectionManager::new(
            config.clone(),
            Arc::clone(&storage_manager),
        ).await.map_err(|_e| NetworkError::PortBindFailed { port: config.api_port })?);

        Ok(Self {
            listener,
            config,
            security_manager,
            node_manager,
            session_manager,
            transport_manager,
            connection_manager,
            storage_manager,
            monitoring_manager,
            stats: Arc::new(RwLock::new(ApiStats::default())),
            running: Arc::new(RwLock::new(false)),
            server_handle: Arc::new(RwLock::new(None)),
        })
    }

    /// 启动API服务器
    pub async fn start(&self) -> NetworkResult<()> {
        let mut running = self.running.write().await;
        if *running {
            return Ok(());
        }

        *running = true;
        drop(running);

        // 创建应用状态
        let app_state = AppState {
            security_manager: Arc::clone(&self.security_manager),
            node_manager: Arc::clone(&self.node_manager),
            session_manager: Arc::clone(&self.session_manager),
            transport_manager: Arc::clone(&self.transport_manager),
            connection_manager: Arc::clone(&self.connection_manager),
            storage_manager: Arc::clone(&self.storage_manager),
            monitoring_manager: Arc::clone(&self.monitoring_manager),
            stats: Arc::clone(&self.stats),
        };

        // 创建路由
        let app = self.create_router(app_state);

        // 获取监听地址
        let addr = format!("{}:{}", self.config.bind_address, self.config.api_port);

        // 重新绑定监听器（因为我们不能移动self.listener）
        let listener = TcpListener::bind(&addr).await
            .map_err(|_e| NetworkError::PortBindFailed { port: self.config.api_port })?;

        let server_handle = tokio::spawn(async move {
            if let Err(e) = axum::serve(listener, app).await {
                error!("API服务器运行错误: {}", e);
            }
        });

        // 保存服务器句柄
        let mut handle = self.server_handle.write().await;
        *handle = Some(server_handle);

        info!("API服务器启动成功，监听地址: {}", addr);
        Ok(())
    }

    /// 停止API服务器
    pub async fn stop(&self) -> NetworkResult<()> {
        let mut running = self.running.write().await;
        if !*running {
            return Ok(());
        }

        *running = false;
        drop(running);

        // 停止服务器
        let mut handle = self.server_handle.write().await;
        if let Some(server_handle) = handle.take() {
            server_handle.abort();
        }

        info!("API服务器停止成功");
        Ok(())
    }

    /// 获取API统计信息
    pub async fn get_stats(&self) -> ApiStats {
        self.stats.read().await.clone()
    }

    /// 创建路由
    fn create_router(&self, state: AppState) -> Router {
        Router::new()
            // 健康检查
            .route("/health", get(health_check))

            // 节点管理API
            .route("/api/v1/node/info", get(get_node_info))
            .route("/api/v1/node/status", get(get_node_status))
            .route("/api/v1/node/stats", get(get_node_stats))
            .route("/api/v1/node/register", post(register_node))
            .route("/api/v1/node/heartbeat", post(send_heartbeat))

            // 会话管理API
            .route("/api/v1/sessions", get(list_sessions))
            .route("/api/v1/sessions", post(create_session))
            .route("/api/v1/sessions/:session_id", get(get_session))
            .route("/api/v1/sessions/:session_id", delete(delete_session))
            .route("/api/v1/sessions/:session_id/stats", get(get_session_stats))

            // 传输管理API
            .route("/api/v1/transports", get(list_transports))
            .route("/api/v1/transports", post(create_transport))
            .route("/api/v1/transports/:transport_id", get(get_transport))
            .route("/api/v1/transports/:transport_id", delete(delete_transport))
            .route("/api/v1/transports/:transport_id/start", post(start_transport))
            .route("/api/v1/transports/:transport_id/stop", post(stop_transport))
            .route("/api/v1/transports/:transport_id/stats", get(get_transport_stats))

            // 连接管理API
            .route("/api/v1/connections", get(list_connections))
            .route("/api/v1/connections", post(create_connection))
            .route("/api/v1/connections/:connection_id", get(get_connection))
            .route("/api/v1/connections/:connection_id", delete(delete_connection))
            .route("/api/v1/connections/:connection_id/stats", get(get_connection_stats))

            // 隧道管理API
            .route("/api/v1/tunnels", get(list_tunnels))
            .route("/api/v1/tunnels", post(create_tunnel))
            .route("/api/v1/tunnels/:tunnel_id", get(get_tunnel))
            .route("/api/v1/tunnels/:tunnel_id", delete(delete_tunnel))
            .route("/api/v1/tunnels/:tunnel_id/stats", get(get_tunnel_stats))

            // 监控API
            .route("/api/v1/metrics", get(get_metrics))
            .route("/api/v1/alerts", get(get_alerts))

            // 配置API
            .route("/api/v1/config", get(get_config))
            .route("/api/v1/config", put(update_config))

            // 中间件
            .layer(
                ServiceBuilder::new()
                    .layer(TraceLayer::new_for_http())
                    .layer(CorsLayer::permissive())
                    .layer(middleware::from_fn_with_state(state.clone(), auth_middleware))
                    .layer(middleware::from_fn_with_state(state.clone(), stats_middleware))
            )
            .with_state(state)
    }
}

// API处理函数

/// 健康检查
async fn health_check() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "version": env!("CARGO_PKG_VERSION")
    }))
}

/// 获取节点信息
async fn get_node_info(State(state): State<AppState>) -> Result<Json<NodeInfo>, ApiError> {
    let node_info = state.node_manager.get_node_info().await;
    Ok(Json(node_info))
}

/// 获取节点状态
async fn get_node_status(State(state): State<AppState>) -> Result<Json<serde_json::Value>, ApiError> {
    let status = state.node_manager.get_status().await;
    Ok(Json(serde_json::json!({
        "status": status,
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

/// 获取节点统计信息
async fn get_node_stats(State(state): State<AppState>) -> Result<Json<serde_json::Value>, ApiError> {
    // 简化实现，返回基础统计信息
    Ok(Json(serde_json::json!({
        "stats": {
            "uptime": "0s",
            "memory_usage": "0MB",
            "cpu_usage": "0%"
        },
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

/// 注册节点
async fn register_node(
    State(state): State<AppState>,
    Json(payload): Json<NodeRegistrationRequest>,
) -> Result<Json<NodeRegistrationResponse>, ApiError> {
    // 简化实现，直接返回成功
    let node_info = state.node_manager.get_node_info().await;

    Ok(Json(NodeRegistrationResponse {
        success: true,
        node_id: node_info.node_id,
        message: "节点注册成功".to_string(),
    }))
}

/// 发送心跳
async fn send_heartbeat(State(state): State<AppState>) -> Result<Json<HeartbeatResponse>, ApiError> {
    // 简化实现，直接返回成功
    Ok(Json(HeartbeatResponse {
        success: true,
        timestamp: chrono::Utc::now(),
        message: "心跳发送成功".to_string(),
    }))
}

// 会话管理API

/// 列出所有会话
async fn list_sessions(State(state): State<AppState>) -> Result<Json<Vec<serde_json::Value>>, ApiError> {
    let sessions = state.session_manager.list_sessions();
    // 转换SessionSummary为JSON
    let session_list: Vec<serde_json::Value> = sessions.into_iter()
        .map(|s| serde_json::json!({
            "session_id": s.session_id,
            "session_type": s.session_type,
            "status": s.status,
            "created_at": s.created_at,
            "active_connections": s.active_connections
        }))
        .collect();
    Ok(Json(session_list))
}

/// 创建会话
async fn create_session(
    State(state): State<AppState>,
    Json(payload): Json<CreateSessionRequest>,
) -> Result<Json<CreateSessionResponse>, ApiError> {
    // 简化实现，创建接收者会话
    let session_id = state.session_manager.create_receiver_session(payload.config).await
        .map_err(|e| ApiError::BadRequest(e.to_string()))?;

    Ok(Json(CreateSessionResponse {
        session_id,
        message: "会话创建成功".to_string(),
    }))
}

/// 获取会话信息
async fn get_session(
    State(state): State<AppState>,
    Path(session_id): Path<SessionId>,
) -> Result<Json<SessionInfo>, ApiError> {
    let session = state.session_manager.get_session_info(session_id.clone()).await
        .map_err(|_e| ApiError::NotFound(format!("会话不存在: {}", session_id)))?;
    Ok(Json(session))
}

/// 删除会话
async fn delete_session(
    State(state): State<AppState>,
    Path(session_id): Path<SessionId>,
) -> Result<Json<DeleteSessionResponse>, ApiError> {
    // 简化实现，直接返回成功
    Ok(Json(DeleteSessionResponse {
        success: true,
        message: "会话删除成功".to_string(),
    }))
}

/// 获取会话统计信息
async fn get_session_stats(
    State(state): State<AppState>,
    Path(session_id): Path<SessionId>,
) -> Result<Json<serde_json::Value>, ApiError> {
    // 简化实现，返回基础统计信息
    Ok(Json(serde_json::json!({
        "session_id": session_id,
        "stats": {
            "bytes_sent": 0,
            "bytes_received": 0,
            "packets_sent": 0,
            "packets_received": 0
        },
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

// 传输管理API

/// 列出所有传输
async fn list_transports(State(state): State<AppState>) -> Result<Json<Vec<TransportInfo>>, ApiError> {
    let transports = state.transport_manager.list_transports().await
        .map_err(|e| ApiError::InternalError(e.to_string()))?;
    Ok(Json(transports))
}

/// 创建传输
async fn create_transport(
    State(state): State<AppState>,
    Json(payload): Json<CreateTransportRequest>,
) -> Result<Json<CreateTransportResponse>, ApiError> {
    let transport_id = state.transport_manager.create_transport(
        payload.transport_type,
        payload.config,
    ).await.map_err(|e| ApiError::BadRequest(e.to_string()))?;

    Ok(Json(CreateTransportResponse {
        transport_id,
        message: "传输创建成功".to_string(),
    }))
}

/// 获取传输信息
async fn get_transport(
    State(state): State<AppState>,
    Path(transport_id): Path<TransportId>,
) -> Result<Json<TransportInfo>, ApiError> {
    let transport = state.transport_manager.get_transport_info(&transport_id).await
        .map_err(|e| ApiError::NotFound(format!("传输不存在: {}", transport_id)))?;
    Ok(Json(transport))
}

/// 删除传输
async fn delete_transport(
    State(state): State<AppState>,
    Path(transport_id): Path<TransportId>,
) -> Result<Json<DeleteTransportResponse>, ApiError> {
    state.transport_manager.remove_transport(&transport_id).await
        .map_err(|e| ApiError::InternalError(e.to_string()))?;

    Ok(Json(DeleteTransportResponse {
        success: true,
        message: "传输删除成功".to_string(),
    }))
}

/// 启动传输
async fn start_transport(
    State(state): State<AppState>,
    Path(transport_id): Path<TransportId>,
) -> Result<Json<TransportActionResponse>, ApiError> {
    state.transport_manager.start_transport(&transport_id).await
        .map_err(|e| ApiError::InternalError(e.to_string()))?;

    Ok(Json(TransportActionResponse {
        success: true,
        message: "传输启动成功".to_string(),
    }))
}

/// 停止传输
async fn stop_transport(
    State(state): State<AppState>,
    Path(transport_id): Path<TransportId>,
) -> Result<Json<TransportActionResponse>, ApiError> {
    state.transport_manager.stop_transport(&transport_id).await
        .map_err(|e| ApiError::InternalError(e.to_string()))?;

    Ok(Json(TransportActionResponse {
        success: true,
        message: "传输停止成功".to_string(),
    }))
}

/// 获取传输统计信息
async fn get_transport_stats(
    State(state): State<AppState>,
    Path(transport_id): Path<TransportId>,
) -> Result<Json<serde_json::Value>, ApiError> {
    let stats = state.transport_manager.get_transport_stats(&transport_id).await
        .map_err(|e| ApiError::NotFound(format!("传输统计不存在: {}", transport_id)))?;

    Ok(Json(serde_json::json!({
        "transport_id": transport_id,
        "stats": stats,
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

// 连接管理API

/// 列出所有连接
async fn list_connections(State(state): State<AppState>) -> Result<Json<Vec<InterNodeConnection>>, ApiError> {
    let connections = state.connection_manager.list_connections().await
        .map_err(|e| ApiError::InternalError(e.to_string()))?;
    Ok(Json(connections))
}

/// 创建连接
async fn create_connection(
    State(state): State<AppState>,
    Json(payload): Json<CreateConnectionRequest>,
) -> Result<Json<CreateConnectionResponse>, ApiError> {
    let connection_id = state.connection_manager.create_inter_node_connection(
        payload.source_node,
        payload.target_node,
        payload.connection_type,
    ).await.map_err(|e| ApiError::BadRequest(e.to_string()))?;

    Ok(Json(CreateConnectionResponse {
        connection_id,
        message: "连接创建成功".to_string(),
    }))
}

/// 获取连接信息
async fn get_connection(
    State(state): State<AppState>,
    Path(connection_id): Path<ConnectionId>,
) -> Result<Json<InterNodeConnection>, ApiError> {
    let connection = state.connection_manager.get_connection_info(&connection_id).await
        .map_err(|e| ApiError::NotFound(format!("连接不存在: {}", connection_id)))?;
    Ok(Json(connection))
}

/// 删除连接
async fn delete_connection(
    State(state): State<AppState>,
    Path(connection_id): Path<ConnectionId>,
) -> Result<Json<DeleteConnectionResponse>, ApiError> {
    state.connection_manager.close_connection(&connection_id).await
        .map_err(|e| ApiError::InternalError(e.to_string()))?;

    Ok(Json(DeleteConnectionResponse {
        success: true,
        message: "连接删除成功".to_string(),
    }))
}

/// 获取连接统计信息
async fn get_connection_stats(
    State(state): State<AppState>,
    Path(connection_id): Path<ConnectionId>,
) -> Result<Json<serde_json::Value>, ApiError> {
    let stats = state.connection_manager.get_connection_stats(&connection_id).await
        .map_err(|e| ApiError::NotFound(format!("连接统计不存在: {}", connection_id)))?;

    Ok(Json(serde_json::json!({
        "connection_id": connection_id,
        "stats": stats,
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

// 隧道管理API

/// 列出所有隧道
async fn list_tunnels(State(state): State<AppState>) -> Result<Json<Vec<TunnelId>>, ApiError> {
    let tunnels = state.connection_manager.list_tunnels().await;
    Ok(Json(tunnels))
}

/// 创建隧道
async fn create_tunnel(
    State(state): State<AppState>,
    Json(payload): Json<CreateTunnelRequest>,
) -> Result<Json<CreateTunnelResponse>, ApiError> {
    let tunnel_id = match payload.tunnel_type.as_str() {
        "tcp" => {
            state.connection_manager.create_tcp_tunnel(
                payload.local_address,
                payload.remote_address,
            ).await.map_err(|e| ApiError::BadRequest(e.to_string()))?
        }
        "udp" => {
            state.connection_manager.create_udp_tunnel(
                payload.local_address,
                payload.remote_address,
            ).await.map_err(|e| ApiError::BadRequest(e.to_string()))?
        }
        _ => return Err(ApiError::BadRequest("不支持的隧道类型".to_string())),
    };

    Ok(Json(CreateTunnelResponse {
        tunnel_id,
        message: "隧道创建成功".to_string(),
    }))
}

/// 获取隧道信息
async fn get_tunnel(
    State(state): State<AppState>,
    Path(tunnel_id): Path<TunnelId>,
) -> Result<Json<serde_json::Value>, ApiError> {
    let tunnel_info = state.connection_manager.get_tunnel_info(&tunnel_id).await
        .map_err(|e| ApiError::NotFound(format!("隧道不存在: {}", tunnel_id)))?;
    Ok(Json(serde_json::json!(tunnel_info)))
}

/// 删除隧道
async fn delete_tunnel(
    State(state): State<AppState>,
    Path(tunnel_id): Path<TunnelId>,
) -> Result<Json<DeleteTunnelResponse>, ApiError> {
    state.connection_manager.remove_tunnel(&tunnel_id).await
        .map_err(|e| ApiError::InternalError(e.to_string()))?;

    Ok(Json(DeleteTunnelResponse {
        success: true,
        message: "隧道删除成功".to_string(),
    }))
}

/// 获取隧道统计信息
async fn get_tunnel_stats(
    State(state): State<AppState>,
    Path(tunnel_id): Path<TunnelId>,
) -> Result<Json<serde_json::Value>, ApiError> {
    let stats = state.connection_manager.get_tunnel_stats(&tunnel_id).await
        .map_err(|e| ApiError::NotFound(format!("隧道统计不存在: {}", tunnel_id)))?;

    Ok(Json(serde_json::json!({
        "tunnel_id": tunnel_id,
        "stats": stats,
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

// 监控API

/// 获取指标
async fn get_metrics(State(state): State<AppState>) -> Result<String, ApiError> {
    let metrics = state.monitoring_manager.get_current_metrics().await
        .map_err(|e| ApiError::InternalError(e.to_string()))?;
    Ok(metrics)
}

/// 获取告警
async fn get_alerts(State(state): State<AppState>) -> Result<Json<serde_json::Value>, ApiError> {
    // 简化实现，返回空告警列表
    Ok(Json(serde_json::json!({
        "alerts": [],
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

// 配置API

/// 获取配置
async fn get_config(State(state): State<AppState>) -> Result<Json<serde_json::Value>, ApiError> {
    // TODO: 实现配置获取
    Ok(Json(serde_json::json!({
        "message": "配置获取功能待实现",
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

/// 更新配置
async fn update_config(
    State(state): State<AppState>,
    Json(payload): Json<serde_json::Value>,
) -> Result<Json<serde_json::Value>, ApiError> {
    // TODO: 实现配置更新
    Ok(Json(serde_json::json!({
        "success": true,
        "message": "配置更新功能待实现",
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

// 中间件

/// 认证中间件
async fn auth_middleware(
    State(state): State<AppState>,
    headers: HeaderMap,
    request: axum::extract::Request,
    next: Next,
) -> Result<Response<Body>, ApiError> {
    let path = request.uri().path();
    let method = request.method().clone();

    // 跳过健康检查的认证
    if path == "/health" {
        return Ok(next.run(request).await);
    }

    // 提取客户端证书（在实际实现中，这应该从TLS连接中获取）
    // 这里简化处理，假设从头部获取证书信息
    let client_cert_header = headers.get("X-Client-Cert");
    let source_ip_header = headers.get("X-Forwarded-For")
        .or_else(|| headers.get("X-Real-IP"));

    // TODO: 实现实际的证书验证逻辑
    // 这里简化处理，只检查是否有证书头部
    if client_cert_header.is_none() && !path.starts_with("/api/v1/node/register") {
        return Err(ApiError::Unauthorized("缺少客户端证书".to_string()));
    }

    // 记录API调用
    debug!(
        method = %method,
        path = %path,
        client_cert = ?client_cert_header.map(|h| h.to_str().unwrap_or("invalid")),
        "API调用"
    );

    Ok(next.run(request).await)
}

/// 统计中间件
async fn stats_middleware(
    State(state): State<AppState>,
    request: axum::extract::Request,
    next: Next,
) -> Response<Body> {
    let start_time = std::time::Instant::now();

    // 更新请求计数
    {
        let mut stats = state.stats.write().await;
        stats.total_requests += 1;
    }

    let response = next.run(request).await;

    let duration = start_time.elapsed();
    let status = response.status();

    // 更新统计信息
    {
        let mut stats = state.stats.write().await;
        if status.is_success() {
            stats.successful_requests += 1;
        } else {
            stats.failed_requests += 1;
        }

        // 更新平均响应时间
        let total_requests = stats.total_requests as f64;
        let current_avg = stats.avg_response_time_ms;
        stats.avg_response_time_ms = (current_avg * (total_requests - 1.0) + duration.as_millis() as f64) / total_requests;
    }

    response
}

// 错误类型

/// API错误类型
#[derive(Debug)]
pub enum ApiError {
    /// 请求错误
    BadRequest(String),
    /// 未授权
    Unauthorized(String),
    /// 禁止访问
    Forbidden(String),
    /// 资源不存在
    NotFound(String),
    /// 内部错误
    InternalError(String),
    /// 速率限制
    RateLimited(String),
}

impl axum::response::IntoResponse for ApiError {
    fn into_response(self) -> Response<Body> {
        let (status, error_message) = match self {
            ApiError::BadRequest(msg) => (StatusCode::BAD_REQUEST, msg),
            ApiError::Unauthorized(msg) => (StatusCode::UNAUTHORIZED, msg),
            ApiError::Forbidden(msg) => (StatusCode::FORBIDDEN, msg),
            ApiError::NotFound(msg) => (StatusCode::NOT_FOUND, msg),
            ApiError::InternalError(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
            ApiError::RateLimited(msg) => (StatusCode::TOO_MANY_REQUESTS, msg),
        };

        let body = Json(serde_json::json!({
            "error": error_message,
            "timestamp": chrono::Utc::now().to_rfc3339()
        }));

        (status, body).into_response()
    }
}

// 请求和响应结构体

/// 节点注册请求
#[derive(Debug, Deserialize)]
pub struct NodeRegistrationRequest {
    pub management_server_url: String,
    pub node_config: serde_json::Value,
}

/// 节点注册响应
#[derive(Debug, Serialize)]
pub struct NodeRegistrationResponse {
    pub success: bool,
    pub node_id: NodeId,
    pub message: String,
}

/// 心跳响应
#[derive(Debug, Serialize)]
pub struct HeartbeatResponse {
    pub success: bool,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub message: String,
}

/// 创建会话请求
#[derive(Debug, Deserialize)]
pub struct CreateSessionRequest {
    pub config: crate::types::ReceiverConfig,
}

/// 创建会话响应
#[derive(Debug, Serialize)]
pub struct CreateSessionResponse {
    pub session_id: SessionId,
    pub message: String,
}

/// 删除会话响应
#[derive(Debug, Serialize)]
pub struct DeleteSessionResponse {
    pub success: bool,
    pub message: String,
}

/// 创建传输请求
#[derive(Debug, Deserialize)]
pub struct CreateTransportRequest {
    pub transport_type: crate::types::TransportType,
    pub config: TransportConfig,
}

/// 创建传输响应
#[derive(Debug, Serialize)]
pub struct CreateTransportResponse {
    pub transport_id: TransportId,
    pub message: String,
}

/// 删除传输响应
#[derive(Debug, Serialize)]
pub struct DeleteTransportResponse {
    pub success: bool,
    pub message: String,
}

/// 传输操作响应
#[derive(Debug, Serialize)]
pub struct TransportActionResponse {
    pub success: bool,
    pub message: String,
}

/// 创建连接请求
#[derive(Debug, Deserialize)]
pub struct CreateConnectionRequest {
    pub source_node: NodeId,
    pub target_node: NodeId,
    pub connection_type: ConnectionType,
}

/// 创建连接响应
#[derive(Debug, Serialize)]
pub struct CreateConnectionResponse {
    pub connection_id: ConnectionId,
    pub message: String,
}

/// 删除连接响应
#[derive(Debug, Serialize)]
pub struct DeleteConnectionResponse {
    pub success: bool,
    pub message: String,
}

/// 创建隧道请求
#[derive(Debug, Deserialize)]
pub struct CreateTunnelRequest {
    pub tunnel_type: String,
    pub local_address: SocketAddr,
    pub remote_address: Option<SocketAddr>,
}

/// 创建隧道响应
#[derive(Debug, Serialize)]
pub struct CreateTunnelResponse {
    pub tunnel_id: TunnelId,
    pub message: String,
}

/// 删除隧道响应
#[derive(Debug, Serialize)]
pub struct DeleteTunnelResponse {
    pub success: bool,
    pub message: String,
}
