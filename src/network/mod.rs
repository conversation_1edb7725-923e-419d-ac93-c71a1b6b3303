// 网络模块
// 提供网络通信相关功能

pub mod api;
pub mod websocket;
pub mod tunnel;
pub mod multicast;

use std::sync::Arc;
use std::net::SocketAddr;
use tokio::net::{TcpListener, UdpSocket};
use tracing::{info, error};

use crate::config::NetworkConfig;
use crate::error::{NetworkError, NetworkResult};

/// 网络管理器
pub struct NetworkManager {
    /// 网络配置
    config: NetworkConfig,
    /// API服务器
    api_server: Option<Arc<api::ApiServer>>,
    /// WebSocket服务器
    websocket_server: Option<Arc<websocket::WebSocketServer>>,
    /// TCP隧道管理器
    tcp_tunnel_manager: Option<Arc<tunnel::TcpTunnelManager>>,
    /// UDP隧道管理器
    udp_tunnel_manager: Option<Arc<tunnel::UdpTunnelManager>>,
    /// 组播管理器
    multicast_manager: Option<Arc<multicast::MulticastManager>>,
}

impl NetworkManager {
    /// 创建新的网络管理器
    pub fn new(config: NetworkConfig) -> Self {
        Self {
            config,
            api_server: None,
            websocket_server: None,
            tcp_tunnel_manager: None,
            udp_tunnel_manager: None,
            multicast_manager: None,
        }
    }

    /// 启动网络服务
    pub async fn start(&mut self) -> NetworkResult<()> {
        info!("启动网络服务");

        // 启动API服务器
        self.start_api_server().await?;

        // 启动WebSocket服务器
        self.start_websocket_server().await?;

        // 启动隧道管理器
        self.start_tunnel_managers().await?;

        // 启动组播管理器
        self.start_multicast_manager().await?;

        info!("网络服务启动完成");
        Ok(())
    }

    /// 停止网络服务
    pub async fn stop(&mut self) -> NetworkResult<()> {
        info!("停止网络服务");

        // 停止各个服务
        if let Some(api_server) = &self.api_server {
            api_server.stop().await?;
        }

        if let Some(websocket_server) = &self.websocket_server {
            websocket_server.stop().await?;
        }

        if let Some(tcp_manager) = &self.tcp_tunnel_manager {
            tcp_manager.stop().await?;
        }

        if let Some(udp_manager) = &self.udp_tunnel_manager {
            udp_manager.stop().await?;
        }

        if let Some(multicast_manager) = &self.multicast_manager {
            multicast_manager.stop().await?;
        }

        info!("网络服务停止完成");
        Ok(())
    }

    /// 启动API服务器
    async fn start_api_server(&mut self) -> NetworkResult<()> {
        let bind_addr = format!("{}:{}", self.config.bind_address, self.config.api_port);
        let listener = TcpListener::bind(&bind_addr).await.map_err(|e| {
            NetworkError::PortBindFailed { port: self.config.api_port }
        })?;

        let api_server = Arc::new(api::ApiServer::new_basic(listener, self.config.clone()).await?);
        api_server.start().await?;
        
        self.api_server = Some(api_server);
        info!("API服务器已启动，监听地址: {}", bind_addr);
        
        Ok(())
    }

    /// 启动WebSocket服务器
    async fn start_websocket_server(&mut self) -> NetworkResult<()> {
        let bind_addr = format!("{}:{}", self.config.bind_address, self.config.websocket_port);
        let listener = TcpListener::bind(&bind_addr).await.map_err(|e| {
            NetworkError::PortBindFailed { port: self.config.websocket_port }
        })?;

        let websocket_server = Arc::new(websocket::WebSocketServer::new(listener, self.config.clone()).await?);
        websocket_server.start().await?;
        
        self.websocket_server = Some(websocket_server);
        info!("WebSocket服务器已启动，监听地址: {}", bind_addr);
        
        Ok(())
    }

    /// 启动隧道管理器
    async fn start_tunnel_managers(&mut self) -> NetworkResult<()> {
        // 启动TCP隧道管理器
        let tcp_manager = Arc::new(tunnel::TcpTunnelManager::new(
            self.config.port_ranges.tcp_tunnel_port_range,
            self.config.clone(),
        ).await?);
        tcp_manager.start().await?;
        self.tcp_tunnel_manager = Some(tcp_manager);

        // 启动UDP隧道管理器
        let udp_manager = Arc::new(tunnel::UdpTunnelManager::new(
            self.config.port_ranges.udp_tunnel_port_range,
            self.config.clone(),
        ).await?);
        udp_manager.start().await?;
        self.udp_tunnel_manager = Some(udp_manager);

        info!("隧道管理器已启动");
        Ok(())
    }

    /// 启动组播管理器
    async fn start_multicast_manager(&mut self) -> NetworkResult<()> {
        let multicast_manager = Arc::new(multicast::MulticastManager::new(
            self.config.port_ranges.multicast_port_range,
            self.config.clone(),
        ).await?);
        multicast_manager.start().await?;
        
        self.multicast_manager = Some(multicast_manager);
        info!("组播管理器已启动");
        
        Ok(())
    }

    /// 获取API服务器
    pub fn api_server(&self) -> Option<Arc<api::ApiServer>> {
        self.api_server.clone()
    }

    /// 获取WebSocket服务器
    pub fn websocket_server(&self) -> Option<Arc<websocket::WebSocketServer>> {
        self.websocket_server.clone()
    }

    /// 获取TCP隧道管理器
    pub fn tcp_tunnel_manager(&self) -> Option<Arc<tunnel::TcpTunnelManager>> {
        self.tcp_tunnel_manager.clone()
    }

    /// 获取UDP隧道管理器
    pub fn udp_tunnel_manager(&self) -> Option<Arc<tunnel::UdpTunnelManager>> {
        self.udp_tunnel_manager.clone()
    }

    /// 获取组播管理器
    pub fn multicast_manager(&self) -> Option<Arc<multicast::MulticastManager>> {
        self.multicast_manager.clone()
    }

    /// 获取网络状态
    pub async fn get_network_status(&self) -> NetworkStatus {
        NetworkStatus {
            api_server_running: self.api_server.is_some(),
            websocket_server_running: self.websocket_server.is_some(),
            tcp_tunnel_manager_running: self.tcp_tunnel_manager.is_some(),
            udp_tunnel_manager_running: self.udp_tunnel_manager.is_some(),
            multicast_manager_running: self.multicast_manager.is_some(),
            api_port: self.config.api_port,
            websocket_port: self.config.websocket_port,
            bind_address: self.config.bind_address.clone(),
        }
    }

    /// 检查端口是否可用
    pub async fn check_port_availability(&self, port: u16) -> bool {
        let bind_addr = format!("{}:{}", self.config.bind_address, port);
        
        // 尝试绑定TCP端口
        if let Ok(listener) = TcpListener::bind(&bind_addr).await {
            drop(listener);
            return true;
        }
        
        false
    }

    /// 获取可用端口
    pub async fn get_available_port(&self, start_port: u16, end_port: u16) -> Option<u16> {
        for port in start_port..=end_port {
            if self.check_port_availability(port).await {
                return Some(port);
            }
        }
        None
    }

    /// 更新网络配置
    pub async fn update_config(&mut self, new_config: NetworkConfig) -> NetworkResult<()> {
        info!("更新网络配置");
        
        // 检查是否需要重启服务
        let need_restart = self.config.bind_address != new_config.bind_address
            || self.config.api_port != new_config.api_port
            || self.config.websocket_port != new_config.websocket_port;

        self.config = new_config;

        if need_restart {
            info!("配置变更需要重启网络服务");
            self.stop().await?;
            self.start().await?;
        }

        Ok(())
    }
}

/// 网络状态
#[derive(Debug, Clone)]
pub struct NetworkStatus {
    /// API服务器是否运行
    pub api_server_running: bool,
    /// WebSocket服务器是否运行
    pub websocket_server_running: bool,
    /// TCP隧道管理器是否运行
    pub tcp_tunnel_manager_running: bool,
    /// UDP隧道管理器是否运行
    pub udp_tunnel_manager_running: bool,
    /// 组播管理器是否运行
    pub multicast_manager_running: bool,
    /// API端口
    pub api_port: u16,
    /// WebSocket端口
    pub websocket_port: u16,
    /// 绑定地址
    pub bind_address: String,
}

/// 网络连接信息
#[derive(Debug, Clone)]
pub struct ConnectionInfo {
    /// 连接ID
    pub connection_id: String,
    /// 本地地址
    pub local_addr: SocketAddr,
    /// 远程地址
    pub remote_addr: SocketAddr,
    /// 连接类型
    pub connection_type: ConnectionType,
    /// 连接状态
    pub status: ConnectionStatus,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 最后活跃时间
    pub last_active: chrono::DateTime<chrono::Utc>,
}

/// 连接类型
#[derive(Debug, Clone, PartialEq)]
pub enum ConnectionType {
    /// HTTP API连接
    HttpApi,
    /// WebSocket连接
    WebSocket,
    /// TCP隧道连接
    TcpTunnel,
    /// UDP隧道连接
    UdpTunnel,
    /// 组播连接
    Multicast,
}

/// 连接状态
#[derive(Debug, Clone, PartialEq)]
pub enum ConnectionStatus {
    /// 连接中
    Connecting,
    /// 已连接
    Connected,
    /// 断开连接
    Disconnected,
    /// 错误
    Error,
}

/// 网络统计信息
#[derive(Debug, Clone, Default)]
pub struct NetworkStats {
    /// 总连接数
    pub total_connections: u64,
    /// 活跃连接数
    pub active_connections: u32,
    /// 发送字节数
    pub bytes_sent: u64,
    /// 接收字节数
    pub bytes_received: u64,
    /// 发送包数
    pub packets_sent: u64,
    /// 接收包数
    pub packets_received: u64,
    /// 错误计数
    pub error_count: u32,
}

impl NetworkManager {
    /// 获取网络统计信息
    pub async fn get_network_stats(&self) -> NetworkStats {
        let mut stats = NetworkStats::default();

        // 收集各个组件的统计信息
        if let Some(api_server) = &self.api_server {
            let api_stats = api_server.get_stats().await;
            stats.total_connections += api_stats.total_connections;
            stats.active_connections += api_stats.active_connections;
            stats.bytes_sent += api_stats.bytes_sent;
            stats.bytes_received += api_stats.bytes_received;
        }

        if let Some(websocket_server) = &self.websocket_server {
            let ws_stats = websocket_server.get_stats().await;
            stats.total_connections += ws_stats.total_connections;
            stats.active_connections += ws_stats.active_connections;
            stats.bytes_sent += ws_stats.bytes_sent;
            stats.bytes_received += ws_stats.bytes_received;
        }

        if let Some(tcp_manager) = &self.tcp_tunnel_manager {
            let tcp_stats = tcp_manager.get_stats().await;
            stats.total_connections += tcp_stats.total_connections;
            stats.active_connections += tcp_stats.active_connections;
            stats.bytes_sent += tcp_stats.bytes_sent;
            stats.bytes_received += tcp_stats.bytes_received;
        }

        if let Some(udp_manager) = &self.udp_tunnel_manager {
            let udp_stats = udp_manager.get_stats().await;
            stats.total_connections += udp_stats.total_connections;
            stats.active_connections += udp_stats.active_connections;
            stats.bytes_sent += udp_stats.bytes_sent;
            stats.bytes_received += udp_stats.bytes_received;
        }

        stats
    }
}
