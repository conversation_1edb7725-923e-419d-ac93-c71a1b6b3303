// 存储管理模块
// 提供内存存储和持久化存储功能

pub mod memory;
pub mod persistent;

use std::sync::Arc;
use async_trait::async_trait;

use crate::config::StorageConfig;
use crate::error::{StorageError, StorageResult};
use crate::types::{
    SessionId, SessionInfo, TransportId, TransportInfo,
    ConnectionId, InterNodeConnection, NodeStatistics,
};
use crate::config::ForwarderConfig;

/// 存储管理器
pub struct StorageManager {
    /// 内存存储
    memory_store: Arc<memory::MemoryStore>,
    /// 持久化存储
    persistent_store: Arc<persistent::PersistentStore>,
}

impl StorageManager {
    /// 创建新的存储管理器
    pub async fn new(config: StorageConfig) -> StorageResult<Self> {
        let memory_store = Arc::new(memory::MemoryStore::new(config.memory));
        let persistent_store = Arc::new(persistent::PersistentStore::new(config.disk).await?);

        Ok(Self {
            memory_store,
            persistent_store,
        })
    }

    /// 获取内存存储引用
    pub fn memory(&self) -> Arc<memory::MemoryStore> {
        self.memory_store.clone()
    }

    /// 获取持久化存储引用
    pub fn persistent(&self) -> Arc<persistent::PersistentStore> {
        self.persistent_store.clone()
    }

    /// 启动存储管理器
    pub async fn start(&self) -> StorageResult<()> {
        // 启动内存存储清理任务
        self.memory_store.start_cleanup_task().await;
        
        // 启动持久化存储同步任务
        self.persistent_store.start_sync_task().await;
        
        Ok(())
    }

    /// 停止存储管理器
    pub async fn stop(&self) -> StorageResult<()> {
        // 停止清理任务
        self.memory_store.stop_cleanup_task().await;
        
        // 停止同步任务并执行最后一次同步
        self.persistent_store.stop_sync_task().await;
        
        Ok(())
    }
}

#[async_trait]
impl SessionStore for StorageManager {
    async fn store_session(&self, session_id: SessionId, session: SessionInfo) -> StorageResult<()> {
        // 存储到内存
        self.memory_store.store_session(session_id.clone(), session.clone()).await?;

        // 异步存储到持久化存储
        self.persistent_store.store_session(session_id, session).await?;

        Ok(())
    }

    async fn get_session(&self, session_id: &SessionId) -> StorageResult<Option<SessionInfo>> {
        // 首先从内存中获取
        if let Some(session) = self.memory_store.get_session(session_id).await? {
            return Ok(Some(session));
        }

        // 从持久化存储中获取
        self.persistent_store.get_session(session_id).await
    }

    async fn remove_session(&self, session_id: &SessionId) -> StorageResult<Option<SessionInfo>> {
        // 从内存中移除
        let session = self.memory_store.remove_session(session_id).await?;

        // 从持久化存储中移除
        self.persistent_store.remove_session(session_id).await?;

        Ok(session)
    }

    async fn list_sessions(&self) -> StorageResult<Vec<SessionInfo>> {
        // 从内存中获取活跃会话
        self.memory_store.list_sessions().await
    }

    async fn update_session_status(&self, session_id: &SessionId, status: crate::types::SessionStatus) -> StorageResult<()> {
        // 更新内存中的状态
        self.memory_store.update_session_status(session_id, status.clone()).await?;

        // 更新持久化存储中的状态
        self.persistent_store.update_session_status(session_id, status).await?;

        Ok(())
    }

    async fn session_exists(&self, session_id: &SessionId) -> bool {
        // 首先检查内存
        if self.memory_store.session_exists(session_id).await {
            return true;
        }

        // 检查持久化存储
        self.persistent_store.session_exists(session_id).await
    }

    async fn session_count(&self) -> usize {
        self.memory_store.session_count().await
    }
}

#[async_trait]
impl TransportStore for StorageManager {
    async fn store_transport(&self, transport_id: TransportId, transport: TransportInfo) -> StorageResult<()> {
        // 存储到内存
        self.memory_store.store_transport(transport_id.clone(), transport.clone()).await?;

        // 异步存储到持久化存储
        let persistent_store = Arc::clone(&self.persistent_store);
        let transport_id_clone = transport_id.clone();
        let transport_clone = transport.clone();
        tokio::spawn(async move {
            let _ = persistent_store.store_transport(transport_id_clone, transport_clone).await;
        });

        Ok(())
    }

    async fn get_transport(&self, transport_id: &TransportId) -> StorageResult<Option<TransportInfo>> {
        // 优先从内存获取
        if let Some(transport) = self.memory_store.get_transport(transport_id).await? {
            return Ok(Some(transport));
        }

        // 从持久化存储获取
        self.persistent_store.get_transport(transport_id).await
    }

    async fn remove_transport(&self, transport_id: &TransportId) -> StorageResult<Option<TransportInfo>> {
        // 从内存删除
        let transport = self.memory_store.remove_transport(transport_id).await?;

        // 异步从持久化存储删除
        let persistent_store = Arc::clone(&self.persistent_store);
        let transport_id_clone = transport_id.clone();
        tokio::spawn(async move {
            let _ = persistent_store.remove_transport(&transport_id_clone).await;
        });

        Ok(transport)
    }

    async fn list_transports(&self) -> StorageResult<Vec<TransportInfo>> {
        // 从内存获取列表
        self.memory_store.list_transports().await
    }

    async fn transport_count(&self) -> usize {
        self.memory_store.transport_count().await
    }
}

#[async_trait]
impl ConnectionStore for StorageManager {
    async fn store_connection(&self, connection_id: ConnectionId, connection: InterNodeConnection) -> StorageResult<()> {
        // 存储到内存
        self.memory_store.store_connection(connection_id.clone(), connection.clone()).await?;

        // 异步存储到持久化存储
        let persistent_store = Arc::clone(&self.persistent_store);
        let connection_id_clone = connection_id.clone();
        let connection_clone = connection.clone();
        tokio::spawn(async move {
            let _ = persistent_store.store_connection(connection_id_clone, connection_clone).await;
        });

        Ok(())
    }

    async fn get_connection(&self, connection_id: &ConnectionId) -> StorageResult<Option<InterNodeConnection>> {
        // 优先从内存获取
        if let Some(connection) = self.memory_store.get_connection(connection_id).await? {
            return Ok(Some(connection));
        }

        // 从持久化存储获取
        self.persistent_store.get_connection(connection_id).await
    }

    async fn remove_connection(&self, connection_id: &ConnectionId) -> StorageResult<Option<InterNodeConnection>> {
        // 从内存删除
        let connection = self.memory_store.remove_connection(connection_id).await?;

        // 异步从持久化存储删除
        let persistent_store = Arc::clone(&self.persistent_store);
        let connection_id_clone = connection_id.clone();
        tokio::spawn(async move {
            let _ = persistent_store.remove_connection(&connection_id_clone).await;
        });

        Ok(connection)
    }

    async fn list_connections(&self) -> StorageResult<Vec<InterNodeConnection>> {
        // 从内存获取列表
        self.memory_store.list_connections().await
    }

    async fn connection_count(&self) -> usize {
        self.memory_store.connection_count().await
    }
}

/// 会话存储接口
#[async_trait]
pub trait SessionStore: Send + Sync {
    /// 存储会话信息
    async fn store_session(&self, session_id: SessionId, session: SessionInfo) -> StorageResult<()>;
    
    /// 获取会话信息
    async fn get_session(&self, session_id: &SessionId) -> StorageResult<Option<SessionInfo>>;
    
    /// 删除会话
    async fn remove_session(&self, session_id: &SessionId) -> StorageResult<Option<SessionInfo>>;
    
    /// 列出所有会话
    async fn list_sessions(&self) -> StorageResult<Vec<SessionInfo>>;
    
    /// 更新会话状态
    async fn update_session_status(&self, session_id: &SessionId, status: crate::types::SessionStatus) -> StorageResult<()>;
    
    /// 检查会话是否存在
    async fn session_exists(&self, session_id: &SessionId) -> bool;
    
    /// 获取会话数量
    async fn session_count(&self) -> usize;
}

/// 传输存储接口
#[async_trait]
pub trait TransportStore: Send + Sync {
    /// 存储传输信息
    async fn store_transport(&self, transport_id: TransportId, transport: TransportInfo) -> StorageResult<()>;
    
    /// 获取传输信息
    async fn get_transport(&self, transport_id: &TransportId) -> StorageResult<Option<TransportInfo>>;
    
    /// 删除传输
    async fn remove_transport(&self, transport_id: &TransportId) -> StorageResult<Option<TransportInfo>>;
    
    /// 列出所有传输
    async fn list_transports(&self) -> StorageResult<Vec<TransportInfo>>;
    
    /// 获取传输数量
    async fn transport_count(&self) -> usize;
}

/// 连接存储接口
#[async_trait]
pub trait ConnectionStore: Send + Sync {
    /// 存储连接信息
    async fn store_connection(&self, connection_id: ConnectionId, connection: InterNodeConnection) -> StorageResult<()>;
    
    /// 获取连接信息
    async fn get_connection(&self, connection_id: &ConnectionId) -> StorageResult<Option<InterNodeConnection>>;
    
    /// 删除连接
    async fn remove_connection(&self, connection_id: &ConnectionId) -> StorageResult<Option<InterNodeConnection>>;
    
    /// 列出所有连接
    async fn list_connections(&self) -> StorageResult<Vec<InterNodeConnection>>;
    
    /// 获取连接数量
    async fn connection_count(&self) -> usize;
}

/// 统计存储接口
#[async_trait]
pub trait StatisticsStore: Send + Sync {
    /// 更新统计信息
    async fn update_statistics<F>(&self, updater: F) -> StorageResult<()>
    where
        F: FnOnce(&mut NodeStatistics) + Send;
    
    /// 获取统计信息
    async fn get_statistics(&self) -> StorageResult<NodeStatistics>;
    
    /// 重置统计信息
    async fn reset_statistics(&self) -> StorageResult<()>;
}

/// 配置存储接口
#[async_trait]
pub trait ConfigStore: Send + Sync {
    /// 保存配置
    async fn save_config(&self, config: &ForwarderConfig) -> StorageResult<()>;
    
    /// 加载配置
    async fn load_config(&self) -> StorageResult<ForwarderConfig>;
    
    /// 备份配置
    async fn backup_config(&self, backup_name: &str) -> StorageResult<()>;
    
    /// 恢复配置
    async fn restore_config(&self, backup_name: &str) -> StorageResult<ForwarderConfig>;
    
    /// 列出配置备份
    async fn list_config_backups(&self) -> StorageResult<Vec<String>>;
}

/// 日志存储接口
#[async_trait]
pub trait LogStore: Send + Sync {
    /// 写入日志
    async fn write_log(&self, log_entry: &str) -> StorageResult<()>;
    
    /// 轮转日志
    async fn rotate_logs(&self) -> StorageResult<()>;
    
    /// 清理过期日志
    async fn cleanup_old_logs(&self, retention_days: u32) -> StorageResult<()>;
    
    /// 获取日志文件列表
    async fn list_log_files(&self) -> StorageResult<Vec<String>>;
}

/// 存储事件
#[derive(Debug, Clone)]
pub enum StorageEvent {
    /// 会话创建
    SessionCreated { session_id: SessionId },
    /// 会话删除
    SessionDeleted { session_id: SessionId },
    /// 传输创建
    TransportCreated { transport_id: TransportId },
    /// 传输删除
    TransportDeleted { transport_id: TransportId },
    /// 连接创建
    ConnectionCreated { connection_id: ConnectionId },
    /// 连接删除
    ConnectionDeleted { connection_id: ConnectionId },
    /// 统计更新
    StatisticsUpdated,
    /// 配置更新
    ConfigUpdated,
    /// 存储错误
    StorageError { error: String },
}

/// 存储事件监听器
#[async_trait]
pub trait StorageEventListener: Send + Sync {
    /// 处理存储事件
    async fn on_storage_event(&self, event: StorageEvent);
}

/// 存储事件发布器
pub struct StorageEventPublisher {
    listeners: Arc<tokio::sync::RwLock<Vec<Arc<dyn StorageEventListener>>>>,
}

impl StorageEventPublisher {
    /// 创建新的事件发布器
    pub fn new() -> Self {
        Self {
            listeners: Arc::new(tokio::sync::RwLock::new(Vec::new())),
        }
    }

    /// 添加事件监听器
    pub async fn add_listener(&self, listener: Arc<dyn StorageEventListener>) {
        let mut listeners = self.listeners.write().await;
        listeners.push(listener);
    }

    /// 移除事件监听器
    pub async fn remove_listener(&self, listener_id: usize) {
        let mut listeners = self.listeners.write().await;
        if listener_id < listeners.len() {
            listeners.remove(listener_id);
        }
    }

    /// 发布事件
    pub async fn publish(&self, event: StorageEvent) {
        let listeners = self.listeners.read().await;
        for listener in listeners.iter() {
            listener.on_storage_event(event.clone()).await;
        }
    }
}

impl Default for StorageEventPublisher {
    fn default() -> Self {
        Self::new()
    }
}

/// 存储健康状态
#[derive(Debug, Clone, Default)]
pub struct StorageHealth {
    /// 内存存储健康状态
    pub memory_healthy: bool,
    /// 持久化存储健康状态
    pub persistent_healthy: bool,
    /// 内存使用率
    pub memory_usage_percent: f32,
    /// 磁盘使用率
    pub disk_usage_percent: f32,
    /// 最后检查时间
    pub last_check: chrono::DateTime<chrono::Utc>,
}

impl StorageManager {
    /// 获取存储健康状态
    pub async fn get_health(&self) -> StorageResult<StorageHealth> {
        let memory_health = self.memory_store.get_health().await;
        let persistent_health = self.persistent_store.get_health().await;
        
        Ok(StorageHealth {
            memory_healthy: memory_health.is_ok(),
            persistent_healthy: persistent_health.is_ok(),
            memory_usage_percent: memory_health.unwrap_or(0.0),
            disk_usage_percent: persistent_health.unwrap_or(0.0),
            last_check: chrono::Utc::now(),
        })
    }
}
