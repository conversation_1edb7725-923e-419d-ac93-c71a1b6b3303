// 内存存储实现
// 提供高性能的内存数据存储和管理

use std::sync::Arc;
use std::time::{Duration, Instant};
use dashmap::DashMap;
use arc_swap::ArcSwap;
use tokio::sync::RwLock;
use tokio::time::interval;
use async_trait::async_trait;
use tracing::{info, warn, debug};

use crate::config::MemoryStorageConfig;
use crate::error::{StorageError, StorageResult};
use crate::types::{
    SessionId, SessionInfo, SessionStatus, SessionSummary,
    TransportId, TransportInfo, TransportStatus,
    ConnectionId, InterNodeConnection, ConnectionStatus,
    NodeStatistics,
};
use crate::storage::{
    SessionStore, TransportStore, ConnectionStore, StatisticsStore,
    StorageEvent, StorageEventPublisher,
};

/// 内存存储实现
pub struct MemoryStore {
    /// 配置
    config: MemoryStorageConfig,
    
    /// 会话存储
    sessions: DashMap<SessionId, SessionInfo>,
    
    /// 传输存储
    transports: DashMap<TransportId, TransportInfo>,
    
    /// 节点间连接存储
    inter_node_connections: DashMap<ConnectionId, InterNodeConnection>,
    
    /// 统计信息存储
    statistics: ArcSwap<NodeStatistics>,
    
    /// 事件发布器
    event_publisher: Arc<StorageEventPublisher>,
    
    /// 清理任务控制
    cleanup_task_handle: Arc<RwLock<Option<tokio::task::JoinHandle<()>>>>,
    
    /// 启动时间
    start_time: Instant,
}

impl MemoryStore {
    /// 创建新的内存存储
    pub fn new(config: MemoryStorageConfig) -> Self {
        Self {
            config,
            sessions: DashMap::new(),
            transports: DashMap::new(),
            inter_node_connections: DashMap::new(),
            statistics: ArcSwap::new(Arc::new(NodeStatistics::default())),
            event_publisher: Arc::new(StorageEventPublisher::new()),
            cleanup_task_handle: Arc::new(RwLock::new(None)),
            start_time: Instant::now(),
        }
    }

    /// 启动清理任务
    pub async fn start_cleanup_task(&self) {
        let mut handle = self.cleanup_task_handle.write().await;
        if handle.is_some() {
            warn!("清理任务已经在运行");
            return;
        }

        let sessions = self.sessions.clone();
        let transports = self.transports.clone();
        let connections = self.inter_node_connections.clone();
        let event_publisher = self.event_publisher.clone();
        let cleanup_interval = Duration::from_secs(self.config.cleanup_interval_seconds);

        let task = tokio::spawn(async move {
            let mut interval = interval(cleanup_interval);
            
            loop {
                interval.tick().await;
                
                debug!("开始执行内存清理任务");
                
                // 清理过期会话
                let expired_sessions = Self::cleanup_expired_sessions(&sessions).await;
                for session_id in expired_sessions {
                    event_publisher.publish(StorageEvent::SessionDeleted { session_id }).await;
                }
                
                // 清理无效传输
                let invalid_transports = Self::cleanup_invalid_transports(&transports).await;
                for transport_id in invalid_transports {
                    event_publisher.publish(StorageEvent::TransportDeleted { transport_id }).await;
                }
                
                // 清理断开的连接
                let disconnected_connections = Self::cleanup_disconnected_connections(&connections).await;
                for connection_id in disconnected_connections {
                    event_publisher.publish(StorageEvent::ConnectionDeleted { connection_id }).await;
                }
                
                debug!("内存清理任务完成");
            }
        });

        *handle = Some(task);
        info!("内存存储清理任务已启动");
    }

    /// 停止清理任务
    pub async fn stop_cleanup_task(&self) {
        let mut handle = self.cleanup_task_handle.write().await;
        if let Some(task) = handle.take() {
            task.abort();
            info!("内存存储清理任务已停止");
        }
    }

    /// 清理过期会话
    async fn cleanup_expired_sessions(sessions: &DashMap<SessionId, SessionInfo>) -> Vec<SessionId> {
        let mut expired = Vec::new();
        let now = chrono::Utc::now();
        
        sessions.retain(|session_id, session| {
            // 检查会话是否过期（超过1小时未更新）
            let is_expired = now.signed_duration_since(session.updated_at).num_seconds() > 3600;
            
            // 检查会话是否处于终止状态
            let is_terminated = matches!(session.status, SessionStatus::Terminated | SessionStatus::Failed);
            
            if is_expired || is_terminated {
                expired.push(session_id.clone());
                false
            } else {
                true
            }
        });
        
        if !expired.is_empty() {
            info!("清理了 {} 个过期或终止的会话", expired.len());
        }
        
        expired
    }

    /// 清理无效传输
    async fn cleanup_invalid_transports(transports: &DashMap<TransportId, TransportInfo>) -> Vec<TransportId> {
        let mut invalid = Vec::new();
        
        transports.retain(|transport_id, transport| {
            let is_invalid = matches!(transport.status, TransportStatus::Error | TransportStatus::Closed);
            
            if is_invalid {
                invalid.push(transport_id.clone());
                false
            } else {
                true
            }
        });
        
        if !invalid.is_empty() {
            info!("清理了 {} 个无效传输", invalid.len());
        }
        
        invalid
    }

    /// 清理断开的连接
    async fn cleanup_disconnected_connections(connections: &DashMap<ConnectionId, InterNodeConnection>) -> Vec<ConnectionId> {
        let mut disconnected = Vec::new();
        let now = chrono::Utc::now();
        
        connections.retain(|connection_id, connection| {
            // 检查连接是否断开
            let is_disconnected = matches!(connection.status, ConnectionStatus::Disconnected | ConnectionStatus::Error);
            
            // 检查连接是否长时间未活跃（超过5分钟）
            let is_inactive = now.signed_duration_since(connection.last_active).num_seconds() > 300;
            
            if is_disconnected || is_inactive {
                disconnected.push(connection_id.clone());
                false
            } else {
                true
            }
        });
        
        if !disconnected.is_empty() {
            info!("清理了 {} 个断开或不活跃的连接", disconnected.len());
        }
        
        disconnected
    }

    /// 获取内存使用情况
    pub async fn get_memory_usage(&self) -> MemoryUsage {
        MemoryUsage {
            session_count: self.sessions.len(),
            transport_count: self.transports.len(),
            connection_count: self.inter_node_connections.len(),
            max_sessions: self.config.max_sessions,
            max_transports: self.config.max_transports,
            uptime_seconds: self.start_time.elapsed().as_secs(),
        }
    }

    /// 获取健康状态
    pub async fn get_health(&self) -> StorageResult<f32> {
        let usage = self.get_memory_usage().await;
        
        // 计算内存使用率
        let session_usage = usage.session_count as f32 / usage.max_sessions as f32;
        let transport_usage = usage.transport_count as f32 / usage.max_transports as f32;
        
        let max_usage = session_usage.max(transport_usage) * 100.0;
        
        Ok(max_usage)
    }

    /// 获取事件发布器
    pub fn event_publisher(&self) -> Arc<StorageEventPublisher> {
        self.event_publisher.clone()
    }
}

/// 内存使用情况
#[derive(Debug, Clone)]
pub struct MemoryUsage {
    /// 当前会话数
    pub session_count: usize,
    /// 当前传输数
    pub transport_count: usize,
    /// 当前连接数
    pub connection_count: usize,
    /// 最大会话数
    pub max_sessions: usize,
    /// 最大传输数
    pub max_transports: usize,
    /// 运行时间(秒)
    pub uptime_seconds: u64,
}

#[async_trait]
impl SessionStore for MemoryStore {
    async fn store_session(&self, session_id: SessionId, session: SessionInfo) -> StorageResult<()> {
        // 检查是否超过最大会话数限制
        if self.sessions.len() >= self.config.max_sessions {
            return Err(StorageError::OutOfMemory);
        }

        self.sessions.insert(session_id.clone(), session);
        self.event_publisher.publish(StorageEvent::SessionCreated { session_id }).await;
        
        Ok(())
    }

    async fn get_session(&self, session_id: &SessionId) -> StorageResult<Option<SessionInfo>> {
        Ok(self.sessions.get(session_id).map(|entry| entry.clone()))
    }

    async fn remove_session(&self, session_id: &SessionId) -> StorageResult<Option<SessionInfo>> {
        let removed = self.sessions.remove(session_id).map(|(_, session)| session);
        
        if removed.is_some() {
            self.event_publisher.publish(StorageEvent::SessionDeleted { 
                session_id: session_id.clone() 
            }).await;
        }
        
        Ok(removed)
    }

    async fn list_sessions(&self) -> StorageResult<Vec<SessionInfo>> {
        Ok(self.sessions.iter().map(|entry| entry.clone()).collect())
    }

    async fn update_session_status(&self, session_id: &SessionId, status: SessionStatus) -> StorageResult<()> {
        if let Some(mut session) = self.sessions.get_mut(session_id) {
            session.status = status;
            session.updated_at = chrono::Utc::now();
            Ok(())
        } else {
            Err(StorageError::DataCorruption { 
                data_type: format!("Session {}", session_id) 
            })
        }
    }

    async fn session_exists(&self, session_id: &SessionId) -> bool {
        self.sessions.contains_key(session_id)
    }

    async fn session_count(&self) -> usize {
        self.sessions.len()
    }
}

#[async_trait]
impl TransportStore for MemoryStore {
    async fn store_transport(&self, transport_id: TransportId, transport: TransportInfo) -> StorageResult<()> {
        // 检查是否超过最大传输数限制
        if self.transports.len() >= self.config.max_transports {
            return Err(StorageError::OutOfMemory);
        }

        self.transports.insert(transport_id.clone(), transport);
        self.event_publisher.publish(StorageEvent::TransportCreated { transport_id }).await;
        
        Ok(())
    }

    async fn get_transport(&self, transport_id: &TransportId) -> StorageResult<Option<TransportInfo>> {
        Ok(self.transports.get(transport_id).map(|entry| entry.clone()))
    }

    async fn remove_transport(&self, transport_id: &TransportId) -> StorageResult<Option<TransportInfo>> {
        let removed = self.transports.remove(transport_id).map(|(_, transport)| transport);
        
        if removed.is_some() {
            self.event_publisher.publish(StorageEvent::TransportDeleted { 
                transport_id: transport_id.clone() 
            }).await;
        }
        
        Ok(removed)
    }

    async fn list_transports(&self) -> StorageResult<Vec<TransportInfo>> {
        Ok(self.transports.iter().map(|entry| entry.clone()).collect())
    }

    async fn transport_count(&self) -> usize {
        self.transports.len()
    }
}

#[async_trait]
impl ConnectionStore for MemoryStore {
    async fn store_connection(&self, connection_id: ConnectionId, connection: InterNodeConnection) -> StorageResult<()> {
        self.inter_node_connections.insert(connection_id.clone(), connection);
        self.event_publisher.publish(StorageEvent::ConnectionCreated { connection_id }).await;
        
        Ok(())
    }

    async fn get_connection(&self, connection_id: &ConnectionId) -> StorageResult<Option<InterNodeConnection>> {
        Ok(self.inter_node_connections.get(connection_id).map(|entry| entry.clone()))
    }

    async fn remove_connection(&self, connection_id: &ConnectionId) -> StorageResult<Option<InterNodeConnection>> {
        let removed = self.inter_node_connections.remove(connection_id).map(|(_, connection)| connection);
        
        if removed.is_some() {
            self.event_publisher.publish(StorageEvent::ConnectionDeleted { 
                connection_id: connection_id.clone() 
            }).await;
        }
        
        Ok(removed)
    }

    async fn list_connections(&self) -> StorageResult<Vec<InterNodeConnection>> {
        Ok(self.inter_node_connections.iter().map(|entry| entry.clone()).collect())
    }

    async fn connection_count(&self) -> usize {
        self.inter_node_connections.len()
    }
}

#[async_trait]
impl StatisticsStore for MemoryStore {
    async fn update_statistics<F>(&self, updater: F) -> StorageResult<()>
    where
        F: FnOnce(&mut NodeStatistics) + Send,
    {
        let current = self.statistics.load();
        let mut new_stats = (**current).clone();
        updater(&mut new_stats);
        self.statistics.store(Arc::new(new_stats));
        
        self.event_publisher.publish(StorageEvent::StatisticsUpdated).await;
        
        Ok(())
    }

    async fn get_statistics(&self) -> StorageResult<NodeStatistics> {
        Ok((**self.statistics.load()).clone())
    }

    async fn reset_statistics(&self) -> StorageResult<()> {
        self.statistics.store(Arc::new(NodeStatistics::default()));
        self.event_publisher.publish(StorageEvent::StatisticsUpdated).await;
        
        Ok(())
    }
}
