// 持久化存储实现
// 提供配置和日志的持久化存储功能

use std::path::PathBuf;
use std::sync::Arc;
use std::time::Duration;
use tokio::fs;
use tokio::sync::RwLock;
use tokio::time::interval;
use async_trait::async_trait;
use tracing::{info, warn, error, debug};

use crate::config::{DiskStorageConfig, ForwarderConfig};
use crate::error::{StorageError, StorageResult};
use crate::storage::{ConfigStore, LogStore, SessionStore, StorageEvent, StorageEventPublisher};
use crate::types::{SessionId, SessionInfo, SessionStatus};

/// 持久化存储实现
pub struct PersistentStore {
    /// 配置
    config: DiskStorageConfig,
    
    /// 基础路径
    base_path: PathBuf,
    
    /// 配置路径
    config_path: PathBuf,
    
    /// 日志路径
    log_path: PathBuf,
    
    /// 备份路径
    backup_path: PathBuf,
    
    /// 事件发布器
    event_publisher: Arc<StorageEventPublisher>,
    
    /// 同步任务控制
    sync_task_handle: Arc<RwLock<Option<tokio::task::JoinHandle<()>>>>,
}

impl PersistentStore {
    /// 创建新的持久化存储
    pub async fn new(config: DiskStorageConfig) -> StorageResult<Self> {
        let base_path = PathBuf::from(&config.base_path);
        let config_path = base_path.join("config");
        let log_path = base_path.join("logs");
        let backup_path = base_path.join("backups");

        // 确保目录存在
        Self::ensure_directories(&[&base_path, &config_path, &log_path, &backup_path]).await?;

        Ok(Self {
            config,
            base_path,
            config_path,
            log_path,
            backup_path,
            event_publisher: Arc::new(StorageEventPublisher::new()),
            sync_task_handle: Arc::new(RwLock::new(None)),
        })
    }

    /// 确保目录存在
    async fn ensure_directories(paths: &[&PathBuf]) -> StorageResult<()> {
        for path in paths {
            if !path.exists() {
                fs::create_dir_all(path).await.map_err(|e| {
                    StorageError::FileOperationFailed {
                        operation: "create_dir_all".to_string(),
                        path: path.to_string_lossy().to_string(),
                        reason: e.to_string(),
                    }
                })?;
                info!("创建目录: {:?}", path);
            }
        }
        Ok(())
    }

    /// 启动同步任务
    pub async fn start_sync_task(&self) {
        let mut handle = self.sync_task_handle.write().await;
        if handle.is_some() {
            warn!("同步任务已经在运行");
            return;
        }

        let log_path = self.log_path.clone();
        let config = self.config.clone();
        let event_publisher = self.event_publisher.clone();
        let sync_interval = Duration::from_secs(config.sync_interval_seconds);

        let task = tokio::spawn(async move {
            let mut interval = interval(sync_interval);
            
            loop {
                interval.tick().await;
                
                debug!("开始执行持久化同步任务");
                
                // 执行日志轮转
                if let Err(e) = Self::rotate_logs_internal(&log_path, &config).await {
                    error!("日志轮转失败: {}", e);
                    event_publisher.publish(StorageEvent::StorageError { 
                        error: format!("日志轮转失败: {}", e) 
                    }).await;
                }
                
                // 清理过期日志
                if let Err(e) = Self::cleanup_old_logs_internal(&log_path, config.log_retention_days).await {
                    error!("清理过期日志失败: {}", e);
                    event_publisher.publish(StorageEvent::StorageError { 
                        error: format!("清理过期日志失败: {}", e) 
                    }).await;
                }
                
                debug!("持久化同步任务完成");
            }
        });

        *handle = Some(task);
        info!("持久化存储同步任务已启动");
    }

    /// 停止同步任务
    pub async fn stop_sync_task(&self) {
        let mut handle = self.sync_task_handle.write().await;
        if let Some(task) = handle.take() {
            task.abort();
            info!("持久化存储同步任务已停止");
        }
        
        // 执行最后一次同步
        if let Err(e) = self.final_sync().await {
            error!("最终同步失败: {}", e);
        }
    }

    /// 执行最终同步
    async fn final_sync(&self) -> StorageResult<()> {
        info!("执行最终同步");
        
        // 执行日志轮转
        Self::rotate_logs_internal(&self.log_path, &self.config).await?;
        
        Ok(())
    }

    /// 内部日志轮转实现
    async fn rotate_logs_internal(log_path: &PathBuf, config: &DiskStorageConfig) -> StorageResult<()> {
        let mut entries = fs::read_dir(log_path).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "read_dir".to_string(),
                path: log_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        let mut log_files = Vec::new();
        while let Some(entry) = entries.next_entry().await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "next_entry".to_string(),
                path: log_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })? {
            let path = entry.path();
            if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("log") {
                if let Ok(metadata) = entry.metadata().await {
                    let size_mb = metadata.len() / (1024 * 1024);
                    if size_mb > config.max_log_size_mb {
                        log_files.push(path);
                    }
                }
            }
        }

        // 轮转超过大小限制的日志文件
        for log_file in log_files {
            let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
            let rotated_name = format!("{}.{}.rotated", 
                log_file.file_stem().unwrap().to_string_lossy(), 
                timestamp
            );
            let rotated_path = log_file.with_file_name(rotated_name);
            
            fs::rename(&log_file, &rotated_path).await.map_err(|e| {
                StorageError::FileOperationFailed {
                    operation: "rename".to_string(),
                    path: log_file.to_string_lossy().to_string(),
                    reason: e.to_string(),
                }
            })?;
            
            debug!("日志文件已轮转: {:?} -> {:?}", log_file, rotated_path);
        }

        Ok(())
    }

    /// 内部清理过期日志实现
    async fn cleanup_old_logs_internal(log_path: &PathBuf, retention_days: u32) -> StorageResult<()> {
        let cutoff_time = chrono::Utc::now() - chrono::Duration::days(retention_days as i64);
        
        let mut entries = fs::read_dir(log_path).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "read_dir".to_string(),
                path: log_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        let mut deleted_count = 0;
        while let Some(entry) = entries.next_entry().await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "next_entry".to_string(),
                path: log_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })? {
            let path = entry.path();
            if path.is_file() {
                if let Ok(metadata) = entry.metadata().await {
                    if let Ok(modified) = metadata.modified() {
                        let modified_time = chrono::DateTime::<chrono::Utc>::from(modified);
                        if modified_time < cutoff_time {
                            if let Err(e) = fs::remove_file(&path).await {
                                warn!("删除过期日志文件失败: {:?}, 错误: {}", path, e);
                            } else {
                                deleted_count += 1;
                                debug!("删除过期日志文件: {:?}", path);
                            }
                        }
                    }
                }
            }
        }

        if deleted_count > 0 {
            info!("清理了 {} 个过期日志文件", deleted_count);
        }

        Ok(())
    }

    /// 获取磁盘使用情况
    pub async fn get_disk_usage(&self) -> StorageResult<DiskUsage> {
        let total_size = Self::calculate_directory_size(&self.base_path).await?;
        
        // 获取磁盘空间信息（简化实现）
        let available_space = 1024 * 1024 * 1024 * 100; // 假设100GB可用空间
        let usage_percent = (total_size as f32 / available_space as f32) * 100.0;
        
        Ok(DiskUsage {
            total_size_bytes: total_size,
            available_space_bytes: available_space,
            usage_percent,
            config_size_bytes: Self::calculate_directory_size(&self.config_path).await?,
            log_size_bytes: Self::calculate_directory_size(&self.log_path).await?,
            backup_size_bytes: Self::calculate_directory_size(&self.backup_path).await?,
        })
    }

    /// 计算目录大小
    fn calculate_directory_size(path: &PathBuf) -> std::pin::Pin<Box<dyn std::future::Future<Output = StorageResult<u64>> + Send + '_>> {
        Box::pin(async move {
            let mut total_size = 0u64;

            if !path.exists() {
                return Ok(0);
            }

            let mut entries = fs::read_dir(path).await.map_err(|e| {
                StorageError::FileOperationFailed {
                    operation: "read_dir".to_string(),
                    path: path.to_string_lossy().to_string(),
                    reason: e.to_string(),
                }
            })?;

            while let Some(entry) = entries.next_entry().await.map_err(|e| {
                StorageError::FileOperationFailed {
                    operation: "next_entry".to_string(),
                    path: path.to_string_lossy().to_string(),
                    reason: e.to_string(),
                }
            })? {
                let entry_path = entry.path();
                if entry_path.is_file() {
                    if let Ok(metadata) = entry.metadata().await {
                        total_size += metadata.len();
                    }
                } else if entry_path.is_dir() {
                    total_size += Self::calculate_directory_size(&entry_path).await?;
                }
            }

            Ok(total_size)
        })
    }

    /// 获取健康状态
    pub async fn get_health(&self) -> StorageResult<f32> {
        let usage = self.get_disk_usage().await?;
        Ok(usage.usage_percent)
    }

    /// 获取事件发布器
    pub fn event_publisher(&self) -> Arc<StorageEventPublisher> {
        self.event_publisher.clone()
    }
}

/// 磁盘使用情况
#[derive(Debug, Clone)]
pub struct DiskUsage {
    /// 总使用大小(字节)
    pub total_size_bytes: u64,
    /// 可用空间(字节)
    pub available_space_bytes: u64,
    /// 使用率(%)
    pub usage_percent: f32,
    /// 配置文件大小(字节)
    pub config_size_bytes: u64,
    /// 日志文件大小(字节)
    pub log_size_bytes: u64,
    /// 备份文件大小(字节)
    pub backup_size_bytes: u64,
}

#[async_trait]
impl ConfigStore for PersistentStore {
    async fn save_config(&self, config: &ForwarderConfig) -> StorageResult<()> {
        let config_file = self.config_path.join("forwarder.json");
        let config_json = serde_json::to_string_pretty(config).map_err(|e| {
            StorageError::SerializationFailed { reason: e.to_string() }
        })?;

        // 原子写入
        let temp_file = config_file.with_extension("tmp");
        fs::write(&temp_file, config_json).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "write".to_string(),
                path: temp_file.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        let config_file_path = config_file.to_string_lossy().to_string();
        fs::rename(temp_file, config_file).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "rename".to_string(),
                path: config_file_path,
                reason: e.to_string(),
            }
        })?;

        self.event_publisher.publish(StorageEvent::ConfigUpdated).await;
        info!("配置已保存");

        Ok(())
    }

    async fn load_config(&self) -> StorageResult<ForwarderConfig> {
        let config_file = self.config_path.join("forwarder.json");
        
        if !config_file.exists() {
            return Err(StorageError::FileOperationFailed {
                operation: "load".to_string(),
                path: config_file.to_string_lossy().to_string(),
                reason: "配置文件不存在".to_string(),
            });
        }

        let config_json = fs::read_to_string(config_file).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "read".to_string(),
                path: self.config_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        let config = serde_json::from_str(&config_json).map_err(|e| {
            StorageError::DeserializationFailed { reason: e.to_string() }
        })?;

        Ok(config)
    }

    async fn backup_config(&self, backup_name: &str) -> StorageResult<()> {
        let config_file = self.config_path.join("forwarder.json");
        let backup_file = self.backup_path.join(format!("{}.json", backup_name));

        if !config_file.exists() {
            return Err(StorageError::FileOperationFailed {
                operation: "backup".to_string(),
                path: config_file.to_string_lossy().to_string(),
                reason: "源配置文件不存在".to_string(),
            });
        }

        fs::copy(&config_file, &backup_file).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "copy".to_string(),
                path: backup_file.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        info!("配置已备份为: {}", backup_name);
        Ok(())
    }

    async fn restore_config(&self, backup_name: &str) -> StorageResult<ForwarderConfig> {
        let backup_file = self.backup_path.join(format!("{}.json", backup_name));
        
        if !backup_file.exists() {
            return Err(StorageError::FileOperationFailed {
                operation: "restore".to_string(),
                path: backup_file.to_string_lossy().to_string(),
                reason: "备份文件不存在".to_string(),
            });
        }

        let config_json = fs::read_to_string(&backup_file).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "read".to_string(),
                path: backup_file.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        let config = serde_json::from_str(&config_json).map_err(|e| {
            StorageError::DeserializationFailed { reason: e.to_string() }
        })?;

        // 恢复到当前配置
        let config_file = self.config_path.join("forwarder.json");
        fs::copy(&backup_file, &config_file).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "copy".to_string(),
                path: config_file.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        self.event_publisher.publish(StorageEvent::ConfigUpdated).await;
        info!("配置已从备份恢复: {}", backup_name);

        Ok(config)
    }

    async fn list_config_backups(&self) -> StorageResult<Vec<String>> {
        let mut backups = Vec::new();
        
        if !self.backup_path.exists() {
            return Ok(backups);
        }

        let mut entries = fs::read_dir(&self.backup_path).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "read_dir".to_string(),
                path: self.backup_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        while let Some(entry) = entries.next_entry().await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "next_entry".to_string(),
                path: self.backup_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })? {
            let path = entry.path();
            if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("json") {
                if let Some(name) = path.file_stem().and_then(|s| s.to_str()) {
                    backups.push(name.to_string());
                }
            }
        }

        backups.sort();
        Ok(backups)
    }
}

#[async_trait]
impl LogStore for PersistentStore {
    async fn write_log(&self, log_entry: &str) -> StorageResult<()> {
        let log_file = self.log_path.join("forwarder.log");
        
        // 追加写入日志
        let mut file = fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(&log_file)
            .await
            .map_err(|e| {
                StorageError::FileOperationFailed {
                    operation: "open".to_string(),
                    path: log_file.to_string_lossy().to_string(),
                    reason: e.to_string(),
                }
            })?;

        use tokio::io::AsyncWriteExt;
        file.write_all(log_entry.as_bytes()).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "write".to_string(),
                path: log_file.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        file.write_all(b"\n").await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "write".to_string(),
                path: log_file.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        Ok(())
    }

    async fn rotate_logs(&self) -> StorageResult<()> {
        Self::rotate_logs_internal(&self.log_path, &self.config).await
    }

    async fn cleanup_old_logs(&self, retention_days: u32) -> StorageResult<()> {
        Self::cleanup_old_logs_internal(&self.log_path, retention_days).await
    }

    async fn list_log_files(&self) -> StorageResult<Vec<String>> {
        let mut log_files = Vec::new();
        
        if !self.log_path.exists() {
            return Ok(log_files);
        }

        let mut entries = fs::read_dir(&self.log_path).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "read_dir".to_string(),
                path: self.log_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        while let Some(entry) = entries.next_entry().await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "next_entry".to_string(),
                path: self.log_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })? {
            let path = entry.path();
            if path.is_file() {
                if let Some(name) = path.file_name().and_then(|s| s.to_str()) {
                    log_files.push(name.to_string());
                }
            }
        }

        log_files.sort();
        Ok(log_files)
    }
}

#[async_trait]
impl SessionStore for PersistentStore {
    async fn store_session(&self, session_id: SessionId, session: SessionInfo) -> StorageResult<()> {
        let session_file = self.base_path.join("sessions").join(format!("{}.json", session_id));

        // 确保会话目录存在
        if let Some(parent) = session_file.parent() {
            fs::create_dir_all(parent).await.map_err(|e| {
                StorageError::FileOperationFailed {
                    operation: "create_dir_all".to_string(),
                    path: parent.to_string_lossy().to_string(),
                    reason: e.to_string(),
                }
            })?;
        }

        // 序列化会话信息
        let session_json = serde_json::to_string_pretty(&session).map_err(|e| {
            StorageError::SerializationFailed {
                reason: format!("SessionInfo: {}", e),
            }
        })?;

        // 写入文件
        fs::write(&session_file, session_json).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "write".to_string(),
                path: session_file.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        debug!("会话已保存到持久化存储: session_id={}", session_id);
        Ok(())
    }

    async fn get_session(&self, session_id: &SessionId) -> StorageResult<Option<SessionInfo>> {
        let session_file = self.base_path.join("sessions").join(format!("{}.json", session_id));

        if !session_file.exists() {
            return Ok(None);
        }

        let session_json = fs::read_to_string(&session_file).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "read".to_string(),
                path: session_file.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        let session: SessionInfo = serde_json::from_str(&session_json).map_err(|e| {
            StorageError::DeserializationFailed {
                reason: format!("SessionInfo: {}", e),
            }
        })?;

        Ok(Some(session))
    }

    async fn remove_session(&self, session_id: &SessionId) -> StorageResult<Option<SessionInfo>> {
        let session_file = self.base_path.join("sessions").join(format!("{}.json", session_id));

        if !session_file.exists() {
            return Ok(None);
        }

        // 先读取会话信息
        let session = self.get_session(session_id).await?;

        // 删除文件
        fs::remove_file(&session_file).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "remove".to_string(),
                path: session_file.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        debug!("会话已从持久化存储中删除: session_id={}", session_id);
        Ok(session)
    }

    async fn list_sessions(&self) -> StorageResult<Vec<SessionInfo>> {
        let sessions_dir = self.base_path.join("sessions");

        if !sessions_dir.exists() {
            return Ok(Vec::new());
        }

        let mut sessions = Vec::new();
        let mut entries = fs::read_dir(&sessions_dir).await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "read_dir".to_string(),
                path: sessions_dir.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })?;

        while let Some(entry) = entries.next_entry().await.map_err(|e| {
            StorageError::FileOperationFailed {
                operation: "next_entry".to_string(),
                path: sessions_dir.to_string_lossy().to_string(),
                reason: e.to_string(),
            }
        })? {
            let path = entry.path();
            if path.is_file() && path.extension().map_or(false, |ext| ext == "json") {
                if let Some(stem) = path.file_stem() {
                    if let Some(session_id) = stem.to_str() {
                        if let Ok(Some(session)) = self.get_session(&session_id.to_string()).await {
                            sessions.push(session);
                        }
                    }
                }
            }
        }

        Ok(sessions)
    }

    async fn update_session_status(&self, session_id: &SessionId, status: SessionStatus) -> StorageResult<()> {
        // 获取现有会话
        if let Some(mut session) = self.get_session(session_id).await? {
            // 更新状态和时间戳
            session.status = status;
            session.updated_at = chrono::Utc::now();

            // 重新保存
            self.store_session(session_id.clone(), session).await?;
        }

        Ok(())
    }

    async fn session_exists(&self, session_id: &SessionId) -> bool {
        let session_file = self.base_path.join("sessions").join(format!("{}.json", session_id));
        session_file.exists()
    }

    async fn session_count(&self) -> usize {
        let sessions_dir = self.base_path.join("sessions");

        if !sessions_dir.exists() {
            return 0;
        }

        let mut count = 0;
        if let Ok(mut entries) = fs::read_dir(&sessions_dir).await {
            while let Ok(Some(entry)) = entries.next_entry().await {
                let path = entry.path();
                if path.is_file() && path.extension().map_or(false, |ext| ext == "json") {
                    count += 1;
                }
            }
        }

        count
    }
}
