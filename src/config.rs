// 转发节点配置管理
// 负责配置的加载、验证、更新和持久化

use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, error};

use crate::error::{ConfigError, ConfigResult};
use crate::types::{NodeId, NodeType, NodeCapabilities};

/// 转发节点完整配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ForwarderConfig {
    /// 节点配置
    pub node: NodeConfig,
    /// 网络配置
    pub network: NetworkConfig,
    /// MediaSoup配置
    pub mediasoup: MediaSoupConfig,
    /// 安全配置
    pub security: SecurityConfig,
    /// 日志配置
    pub logging: LoggingConfig,
    /// 监控配置
    pub monitoring: MonitoringConfig,
    /// 存储配置
    pub storage: StorageConfig,
}

/// 节点基础配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NodeConfig {
    /// 节点ID
    pub node_id: NodeId,
    /// 节点类型
    pub node_type: NodeType,
    /// 监听IP地址
    pub listen_ip: String,
    /// 监听端口
    pub listen_port: u16,
    /// 所属区域
    pub region: String,
    /// 节点能力
    pub capabilities: NodeCapabilities,
    /// 转发管理服务器地址
    pub management_server_url: String,
    /// 心跳间隔(秒)
    pub heartbeat_interval_seconds: u64,
    /// 会话超时时间(秒)
    pub session_timeout_seconds: u64,
}

/// 网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    /// 绑定地址
    pub bind_address: String,
    /// API端口
    pub api_port: u16,
    /// WebSocket端口
    pub websocket_port: u16,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接超时时间(秒)
    pub connection_timeout_seconds: u64,
    /// 读取超时时间(秒)
    pub read_timeout_seconds: u64,
    /// 写入超时时间(秒)
    pub write_timeout_seconds: u64,
    /// TCP保活配置
    pub tcp_keepalive: TcpKeepaliveConfig,
    /// 端口范围配置
    pub port_ranges: PortRangeConfig,
}

/// TCP保活配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TcpKeepaliveConfig {
    /// 是否启用TCP保活
    pub enabled: bool,
    /// 保活时间(秒)
    pub keepalive_time_seconds: u64,
    /// 保活间隔(秒)
    pub keepalive_interval_seconds: u64,
    /// 保活重试次数
    pub keepalive_retries: u32,
}

/// 端口范围配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortRangeConfig {
    /// MediaSoup端口范围
    pub mediasoup_port_range: (u16, u16),
    /// TCP隧道端口范围
    pub tcp_tunnel_port_range: (u16, u16),
    /// UDP隧道端口范围
    pub udp_tunnel_port_range: (u16, u16),
    /// 组播端口范围
    pub multicast_port_range: (u16, u16),
}

/// MediaSoup配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MediaSoupConfig {
    /// Worker数量
    pub worker_count: u32,
    /// 每个Worker的最大连接数
    pub max_connections_per_worker: u32,
    /// RTC端口范围
    pub rtc_port_range: (u16, u16),
    /// 日志级别
    pub log_level: String,
    /// 日志标签
    pub log_tags: Vec<String>,
    /// DTLS证书文件路径
    pub dtls_cert_file: Option<String>,
    /// DTLS私钥文件路径
    pub dtls_key_file: Option<String>,
}

/// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// 节点证书文件路径
    pub node_certificate_path: String,
    /// 节点私钥文件路径
    pub node_private_key_path: String,
    /// CA证书文件路径
    pub ca_certificate_path: String,
    /// 证书轮换天数
    pub certificate_rotation_days: u32,
    /// 证书检查间隔
    pub certificate_check_interval_hours: u64,
    /// 信任的管理服务器列表
    pub trusted_servers: Vec<TrustedServer>,
    /// API权限配置
    pub api_permissions: ApiPermissionConfig,
    /// 速率限制配置
    pub rate_limits: RateLimitConfig,
}

/// 信任的服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustedServer {
    /// 服务器ID
    pub server_id: String,
    /// 服务器名称
    pub server_name: String,
    /// 证书指纹
    pub certificate_fingerprint: String,
    /// 允许的API列表
    pub allowed_apis: Vec<String>,
    /// 是否活跃
    pub is_active: bool,
}

/// API权限配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiPermissionConfig {
    /// 仅管理服务器可调用的API
    pub management_only_apis: Vec<String>,
    /// 仅节点间可调用的API
    pub inter_node_only_apis: Vec<String>,
    /// 两者都可调用的API
    pub both_callable_apis: Vec<String>,
}

/// 速率限制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// 是否启用速率限制
    pub enabled: bool,
    /// 每分钟最大请求数
    pub max_requests_per_minute: u32,
    /// 突发请求数
    pub burst_size: u32,
    /// 限制窗口大小(秒)
    pub window_size_seconds: u64,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 日志格式
    pub format: LogFormat,
    /// 日志输出目标
    pub targets: Vec<LogTarget>,
    /// 日志文件配置
    pub file: LogFileConfig,
    /// 结构化日志字段
    pub structured_fields: Vec<String>,
}

/// 日志格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogFormat {
    /// JSON格式
    Json,
    /// 文本格式
    Text,
    /// 紧凑格式
    Compact,
}

/// 日志输出目标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogTarget {
    /// 标准输出
    Stdout,
    /// 标准错误
    Stderr,
    /// 文件
    File,
    /// 系统日志
    Syslog,
}

/// 日志文件配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogFileConfig {
    /// 日志文件路径
    pub path: String,
    /// 最大文件大小(MB)
    pub max_size_mb: u64,
    /// 保留文件数量
    pub max_files: u32,
    /// 是否压缩旧文件
    pub compress: bool,
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// 是否启用监控
    pub enabled: bool,
    /// Prometheus指标端口
    pub metrics_port: u16,
    /// 指标收集间隔(秒)
    pub metrics_interval_seconds: u64,
    /// 健康检查端口
    pub health_check_port: u16,
    /// 告警配置
    pub alerts: AlertConfig,
}

/// 告警配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertConfig {
    /// 是否启用告警
    pub enabled: bool,
    /// 告警接收器URL
    pub webhook_url: Option<String>,
    /// 告警阈值
    pub thresholds: AlertThresholds,
}

/// 告警阈值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThresholds {
    /// CPU使用率阈值(%)
    pub cpu_usage_threshold: f32,
    /// 内存使用率阈值(%)
    pub memory_usage_threshold: f32,
    /// 磁盘使用率阈值(%)
    pub disk_usage_threshold: f32,
    /// 错误率阈值(%)
    pub error_rate_threshold: f32,
}

/// 存储配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    /// 内存存储配置
    pub memory: MemoryStorageConfig,
    /// 磁盘存储配置
    pub disk: DiskStorageConfig,
}

/// 内存存储配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryStorageConfig {
    /// 最大会话数
    pub max_sessions: usize,
    /// 最大传输数
    pub max_transports: usize,
    /// 清理间隔(秒)
    pub cleanup_interval_seconds: u64,
}

/// 磁盘存储配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskStorageConfig {
    /// 基础路径
    pub base_path: String,
    /// 最大日志大小(MB)
    pub max_log_size_mb: u64,
    /// 日志保留天数
    pub log_retention_days: u32,
    /// 同步间隔(秒)
    pub sync_interval_seconds: u64,
}

/// 配置管理器
pub struct ConfigManager {
    /// 当前配置
    current_config: Arc<RwLock<ForwarderConfig>>,
    /// 配置文件路径
    config_path: PathBuf,
}

impl ConfigManager {
    /// 创建新的配置管理器
    pub fn new(config_path: PathBuf) -> Self {
        Self {
            current_config: Arc::new(RwLock::new(Self::default_config())),
            config_path,
        }
    }

    /// 加载配置文件
    pub async fn load_config(&self) -> ConfigResult<()> {
        info!("正在加载配置文件: {:?}", self.config_path);
        
        if !self.config_path.exists() {
            warn!("配置文件不存在，使用默认配置");
            return Ok(());
        }

        let config_content = tokio::fs::read_to_string(&self.config_path)
            .await
            .map_err(|e| ConfigError::ConfigFileNotFound {
                path: self.config_path.to_string_lossy().to_string(),
            })?;

        let config: ForwarderConfig = if self.config_path.extension()
            .and_then(|s| s.to_str()) == Some("yaml") {
            serde_yaml::from_str(&config_content)
                .map_err(|e| ConfigError::ConfigFormatError {
                    reason: e.to_string(),
                })?
        } else {
            serde_json::from_str(&config_content)
                .map_err(|e| ConfigError::ConfigFormatError {
                    reason: e.to_string(),
                })?
        };

        // 验证配置
        self.validate_config(&config)?;

        // 更新当前配置
        let mut current = self.current_config.write().await;
        *current = config;

        info!("配置加载完成");
        Ok(())
    }

    /// 获取当前配置
    pub async fn get_config(&self) -> ForwarderConfig {
        self.current_config.read().await.clone()
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: ForwarderConfig) -> ConfigResult<()> {
        // 验证新配置
        self.validate_config(&new_config)?;

        // 更新配置
        let mut current = self.current_config.write().await;
        *current = new_config;

        info!("配置更新完成");
        Ok(())
    }

    /// 保存配置到文件
    pub async fn save_config(&self) -> ConfigResult<()> {
        let config = self.current_config.read().await;
        
        let config_content = if self.config_path.extension()
            .and_then(|s| s.to_str()) == Some("yaml") {
            serde_yaml::to_string(&*config)
                .map_err(|e| ConfigError::ConfigFormatError {
                    reason: e.to_string(),
                })?
        } else {
            serde_json::to_string_pretty(&*config)
                .map_err(|e| ConfigError::ConfigFormatError {
                    reason: e.to_string(),
                })?
        };

        tokio::fs::write(&self.config_path, config_content)
            .await
            .map_err(|e| ConfigError::ConfigUpdateFailed {
                reason: e.to_string(),
            })?;

        info!("配置已保存到文件: {:?}", self.config_path);
        Ok(())
    }

    /// 验证配置
    fn validate_config(&self, config: &ForwarderConfig) -> ConfigResult<()> {
        // 验证节点ID
        if config.node.node_id.is_empty() {
            return Err(ConfigError::ConfigValidationFailed {
                field: "node.node_id".to_string(),
                reason: "节点ID不能为空".to_string(),
            });
        }

        // 验证端口范围
        if config.network.api_port == 0 {
            return Err(ConfigError::ConfigValidationFailed {
                field: "network.api_port".to_string(),
                reason: "API端口不能为0".to_string(),
            });
        }

        // 验证证书文件路径
        if config.security.node_certificate_path.is_empty() {
            return Err(ConfigError::ConfigValidationFailed {
                field: "security.node_certificate_path".to_string(),
                reason: "节点证书路径不能为空".to_string(),
            });
        }

        Ok(())
    }

    /// 默认配置
    fn default_config() -> ForwarderConfig {
        ForwarderConfig {
            node: NodeConfig {
                node_id: "forwarder-node-001".to_string(),
                node_type: NodeType::Forwarder,
                listen_ip: "0.0.0.0".to_string(),
                listen_port: 8080,
                region: "default".to_string(),
                capabilities: NodeCapabilities {
                    max_connections: 1000,
                    max_bandwidth_mbps: 1000,
                    supported_codecs: vec!["H264".to_string(), "VP8".to_string()],
                    supported_protocols: vec!["WebRTC".to_string(), "TCP".to_string(), "UDP".to_string()],
                    supports_mediasoup: true,
                    supports_tcp_tunnel: true,
                    supports_udp_tunnel: true,
                    supports_multicast: true,
                },
                management_server_url: "https://management.example.com".to_string(),
                heartbeat_interval_seconds: 30,
                session_timeout_seconds: 300,
            },
            network: NetworkConfig {
                bind_address: "0.0.0.0".to_string(),
                api_port: 8080,
                websocket_port: 8081,
                max_connections: 1000,
                connection_timeout_seconds: 30,
                read_timeout_seconds: 30,
                write_timeout_seconds: 30,
                tcp_keepalive: TcpKeepaliveConfig {
                    enabled: true,
                    keepalive_time_seconds: 600,
                    keepalive_interval_seconds: 60,
                    keepalive_retries: 3,
                },
                port_ranges: PortRangeConfig {
                    mediasoup_port_range: (10000, 20000),
                    tcp_tunnel_port_range: (20001, 30000),
                    udp_tunnel_port_range: (30001, 40000),
                    multicast_port_range: (40001, 50000),
                },
            },
            mediasoup: MediaSoupConfig {
                worker_count: 4,
                max_connections_per_worker: 250,
                rtc_port_range: (10000, 20000),
                log_level: "warn".to_string(),
                log_tags: vec!["info".to_string(), "ice".to_string(), "dtls".to_string()],
                dtls_cert_file: None,
                dtls_key_file: None,
            },
            security: SecurityConfig {
                node_certificate_path: "/etc/ssl/certs/forwarder-node.pem".to_string(),
                node_private_key_path: "/etc/ssl/private/forwarder-node.key".to_string(),
                ca_certificate_path: "/etc/ssl/certs/ca.pem".to_string(),
                certificate_rotation_days: 90,
                certificate_check_interval_hours: 1,
                trusted_servers: vec![],
                api_permissions: ApiPermissionConfig {
                    management_only_apis: vec![
                        "POST /api/v1/sessions/transmitter".to_string(),
                        "DELETE /api/v1/sessions/transmitter/*".to_string(),
                        "POST /api/v1/sessions/receiver".to_string(),
                        "DELETE /api/v1/sessions/receiver/*".to_string(),
                    ],
                    inter_node_only_apis: vec![
                        "POST /api/v1/inter-node/access-request".to_string(),
                        "GET /api/v1/inter-node/connections/*".to_string(),
                    ],
                    both_callable_apis: vec![
                        "GET /api/v1/status".to_string(),
                        "GET /health".to_string(),
                    ],
                },
                rate_limits: RateLimitConfig {
                    enabled: true,
                    max_requests_per_minute: 100,
                    burst_size: 10,
                    window_size_seconds: 60,
                },
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                format: LogFormat::Json,
                targets: vec![LogTarget::Stdout, LogTarget::File],
                file: LogFileConfig {
                    path: "/var/log/kvm_tunnel/forwarder.log".to_string(),
                    max_size_mb: 100,
                    max_files: 10,
                    compress: true,
                },
                structured_fields: vec![
                    "timestamp".to_string(),
                    "level".to_string(),
                    "target".to_string(),
                    "message".to_string(),
                ],
            },
            monitoring: MonitoringConfig {
                enabled: true,
                metrics_port: 9090,
                metrics_interval_seconds: 15,
                health_check_port: 8082,
                alerts: AlertConfig {
                    enabled: false,
                    webhook_url: None,
                    thresholds: AlertThresholds {
                        cpu_usage_threshold: 80.0,
                        memory_usage_threshold: 85.0,
                        disk_usage_threshold: 90.0,
                        error_rate_threshold: 5.0,
                    },
                },
            },
            storage: StorageConfig {
                memory: MemoryStorageConfig {
                    max_sessions: 10000,
                    max_transports: 50000,
                    cleanup_interval_seconds: 300,
                },
                disk: DiskStorageConfig {
                    base_path: "/var/lib/kvm_tunnel".to_string(),
                    max_log_size_mb: 100,
                    log_retention_days: 30,
                    sync_interval_seconds: 60,
                },
            },
        }
    }
}

impl ForwarderConfig {
    /// 从文件加载配置
    pub async fn load() -> ConfigResult<Self> {
        let config_path = std::env::var("KVM_TUNNEL_CONFIG")
            .unwrap_or_else(|_| "/etc/kvm_tunnel/config.yaml".to_string());
        
        let manager = ConfigManager::new(PathBuf::from(config_path));
        manager.load_config().await?;
        Ok(manager.get_config().await)
    }

    /// 从指定路径加载配置
    pub async fn load_from_path(path: &str) -> ConfigResult<Self> {
        let manager = ConfigManager::new(PathBuf::from(path));
        manager.load_config().await?;
        Ok(manager.get_config().await)
    }
}
