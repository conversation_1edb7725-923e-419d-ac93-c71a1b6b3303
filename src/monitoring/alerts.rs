// 告警管理器
// 提供基于阈值的告警功能，支持多种通知方式

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{info, warn, error, debug};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::config::{AlertConfig, AlertThresholds};
use crate::error::{ForwarderError, Result};
use crate::monitoring::metrics::{MetricsCollector, SystemSummary};

/// 告警管理器
pub struct AlertManager {
    /// 告警配置
    config: Arc<RwLock<AlertConfig>>,
    /// 活跃告警
    active_alerts: Arc<RwLock<HashMap<String, Alert>>>,
    /// 告警历史
    alert_history: Arc<RwLock<Vec<Alert>>>,
    /// 告警抑制器
    alert_suppressor: Arc<RwLock<HashMap<String, Instant>>>,
    /// HTTP客户端
    http_client: reqwest::Client,
    /// 告警检查任务句柄
    check_task: Arc<RwLock<Option<tokio::task::JoinHandle<()>>>>,
}

impl AlertManager {
    /// 创建新的告警管理器
    pub async fn new(config: AlertConfig) -> Result<Self> {
        let http_client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .map_err(|e| ForwarderError::InternalError(format!("创建HTTP客户端失败: {}", e)))?;

        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            active_alerts: Arc::new(RwLock::new(HashMap::new())),
            alert_history: Arc::new(RwLock::new(Vec::new())),
            alert_suppressor: Arc::new(RwLock::new(HashMap::new())),
            http_client,
            check_task: Arc::new(RwLock::new(None)),
        })
    }

    /// 启动告警管理器
    pub async fn start(&self) -> Result<()> {
        let config = self.config.read().await;

        if !config.enabled {
            info!("告警功能已禁用");
            return Ok(());
        }

        info!("启动告警管理器");

        // 启动告警检查任务
        self.start_alert_check_task().await;

        info!("告警管理器启动完成");
        Ok(())
    }

    /// 停止告警管理器
    pub async fn stop(&self) -> Result<()> {
        info!("停止告警管理器");

        // 停止告警检查任务
        self.stop_alert_check_task().await;

        info!("告警管理器停止完成");
        Ok(())
    }

    /// 启动告警检查任务
    async fn start_alert_check_task(&self) {
        let mut task = self.check_task.write().await;
        if task.is_some() {
            warn!("告警检查任务已经在运行");
            return;
        }

        let config = self.config.clone();
        let active_alerts = self.active_alerts.clone();
        let alert_history = self.alert_history.clone();
        let alert_suppressor = self.alert_suppressor.clone();
        let http_client = self.http_client.clone();

        let check_task = tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(60)); // 每分钟检查一次

            loop {
                interval.tick().await;

                let config = config.read().await;
                if !config.enabled {
                    continue;
                }

                // 清理过期的告警抑制
                Self::cleanup_alert_suppression(&alert_suppressor).await;

                // 清理过期的活跃告警
                Self::cleanup_expired_alerts(&active_alerts).await;

                // 限制告警历史大小
                Self::limit_alert_history(&alert_history).await;
            }
        });

        *task = Some(check_task);
        info!("告警检查任务已启动");
    }

    /// 停止告警检查任务
    async fn stop_alert_check_task(&self) {
        let mut task = self.check_task.write().await;
        if let Some(task_handle) = task.take() {
            task_handle.abort();
            info!("告警检查任务已停止");
        }
    }

    /// 检查告警条件
    pub async fn check_alert_conditions(&self, metrics: &MetricsCollector) -> Result<()> {
        let config = self.config.read().await;
        if !config.enabled {
            return Ok(());
        }

        debug!("检查告警条件");

        // 获取系统摘要
        let system_summary = metrics.get_system_summary().await;

        // 检查各种告警条件
        self.check_cpu_alert(&config.thresholds, &system_summary).await?;
        self.check_memory_alert(&config.thresholds, &system_summary).await?;
        self.check_disk_alert(&config.thresholds, &system_summary).await?;

        Ok(())
    }

    /// 检查CPU告警
    async fn check_cpu_alert(&self, thresholds: &AlertThresholds, summary: &SystemSummary) -> Result<()> {
        let alert_id = "cpu_usage_high".to_string();

        if summary.cpu_usage_percent > thresholds.cpu_usage_threshold as f64 {
            let alert = Alert {
                id: alert_id.clone(),
                alert_type: AlertType::CpuUsageHigh,
                severity: AlertSeverity::Warning,
                message: format!("CPU使用率过高: {:.1}%", summary.cpu_usage_percent),
                value: summary.cpu_usage_percent,
                threshold: thresholds.cpu_usage_threshold as f64,
                timestamp: Utc::now(),
                resolved: false,
            };

            self.trigger_alert(alert).await?;
        } else {
            self.resolve_alert(&alert_id).await?;
        }

        Ok(())
    }

    /// 检查内存告警
    async fn check_memory_alert(&self, thresholds: &AlertThresholds, summary: &SystemSummary) -> Result<()> {
        let alert_id = "memory_usage_high".to_string();

        if summary.memory_usage_percent > thresholds.memory_usage_threshold as f64 {
            let alert = Alert {
                id: alert_id.clone(),
                alert_type: AlertType::MemoryUsageHigh,
                severity: AlertSeverity::Warning,
                message: format!("内存使用率过高: {:.1}%", summary.memory_usage_percent),
                value: summary.memory_usage_percent,
                threshold: thresholds.memory_usage_threshold as f64,
                timestamp: Utc::now(),
                resolved: false,
            };

            self.trigger_alert(alert).await?;
        } else {
            self.resolve_alert(&alert_id).await?;
        }

        Ok(())
    }

    /// 检查磁盘告警
    async fn check_disk_alert(&self, thresholds: &AlertThresholds, summary: &SystemSummary) -> Result<()> {
        let alert_id = "disk_usage_high".to_string();

        if summary.disk_usage_percent > thresholds.disk_usage_threshold as f64 {
            let alert = Alert {
                id: alert_id.clone(),
                alert_type: AlertType::DiskUsageHigh,
                severity: AlertSeverity::Warning,
                message: format!("磁盘使用率过高: {:.1}%", summary.disk_usage_percent),
                value: summary.disk_usage_percent,
                threshold: thresholds.disk_usage_threshold as f64,
                timestamp: Utc::now(),
                resolved: false,
            };

            self.trigger_alert(alert).await?;
        } else {
            self.resolve_alert(&alert_id).await?;
        }

        Ok(())
    }

    /// 触发告警
    async fn trigger_alert(&self, alert: Alert) -> Result<()> {
        let alert_id = alert.id.clone();

        // 检查是否被抑制
        if self.is_alert_suppressed(&alert_id).await {
            debug!("告警被抑制: {}", alert_id);
            return Ok(());
        }

        // 检查是否已经是活跃告警
        {
            let active_alerts = self.active_alerts.read().await;
            if active_alerts.contains_key(&alert_id) {
                debug!("告警已经活跃: {}", alert_id);
                return Ok(());
            }
        }

        warn!("触发告警: {} - {}", alert_id, alert.message);

        // 添加到活跃告警
        {
            let mut active_alerts = self.active_alerts.write().await;
            active_alerts.insert(alert_id.clone(), alert.clone());
        }

        // 添加到告警历史
        {
            let mut alert_history = self.alert_history.write().await;
            alert_history.push(alert.clone());
        }

        // 发送告警通知
        self.send_alert_notification(&alert).await?;

        // 设置告警抑制
        self.suppress_alert(&alert_id, Duration::from_secs(300)).await; // 5分钟抑制

        Ok(())
    }

    /// 解决告警
    async fn resolve_alert(&self, alert_id: &str) -> Result<()> {
        let mut active_alerts = self.active_alerts.write().await;

        if let Some(mut alert) = active_alerts.remove(alert_id) {
            alert.resolved = true;
            alert.timestamp = Utc::now();

            info!("解决告警: {} - {}", alert_id, alert.message);

            // 添加到告警历史
            let mut alert_history = self.alert_history.write().await;
            alert_history.push(alert.clone());

            // 发送解决通知
            self.send_resolution_notification(&alert).await?;
        }

        Ok(())
    }

    /// 发送告警通知
    async fn send_alert_notification(&self, alert: &Alert) -> Result<()> {
        let config = self.config.read().await;

        if let Some(webhook_url) = &config.webhook_url {
            let notification = AlertNotification {
                alert_type: "alert".to_string(),
                alert: alert.clone(),
            };

            match self.http_client
                .post(webhook_url)
                .json(&notification)
                .send()
                .await
            {
                Ok(response) => {
                    if response.status().is_success() {
                        debug!("告警通知发送成功: {}", alert.id);
                    } else {
                        warn!("告警通知发送失败: {} - 状态码: {}", alert.id, response.status());
                    }
                }
                Err(e) => {
                    error!("发送告警通知失败: {} - 错误: {}", alert.id, e);
                }
            }
        }

        Ok(())
    }

    /// 发送解决通知
    async fn send_resolution_notification(&self, alert: &Alert) -> Result<()> {
        let config = self.config.read().await;

        if let Some(webhook_url) = &config.webhook_url {
            let notification = AlertNotification {
                alert_type: "resolved".to_string(),
                alert: alert.clone(),
            };

            match self.http_client
                .post(webhook_url)
                .json(&notification)
                .send()
                .await
            {
                Ok(response) => {
                    if response.status().is_success() {
                        debug!("解决通知发送成功: {}", alert.id);
                    } else {
                        warn!("解决通知发送失败: {} - 状态码: {}", alert.id, response.status());
                    }
                }
                Err(e) => {
                    error!("发送解决通知失败: {} - 错误: {}", alert.id, e);
                }
            }
        }

        Ok(())
    }

    /// 检查告警是否被抑制
    async fn is_alert_suppressed(&self, alert_id: &str) -> bool {
        let suppressor = self.alert_suppressor.read().await;

        if let Some(suppressed_until) = suppressor.get(alert_id) {
            Instant::now() < *suppressed_until
        } else {
            false
        }
    }

    /// 抑制告警
    async fn suppress_alert(&self, alert_id: &str, duration: Duration) {
        let mut suppressor = self.alert_suppressor.write().await;
        suppressor.insert(alert_id.to_string(), Instant::now() + duration);
    }

    /// 清理过期的告警抑制
    async fn cleanup_alert_suppression(suppressor: &Arc<RwLock<HashMap<String, Instant>>>) {
        let mut suppressor = suppressor.write().await;
        let now = Instant::now();
        suppressor.retain(|_, suppressed_until| now < *suppressed_until);
    }

    /// 清理过期的活跃告警
    async fn cleanup_expired_alerts(active_alerts: &Arc<RwLock<HashMap<String, Alert>>>) {
        let mut alerts = active_alerts.write().await;
        let cutoff = Utc::now() - chrono::Duration::hours(24); // 24小时后清理
        alerts.retain(|_, alert| alert.timestamp > cutoff);
    }

    /// 限制告警历史大小
    async fn limit_alert_history(alert_history: &Arc<RwLock<Vec<Alert>>>) {
        let mut history = alert_history.write().await;
        const MAX_HISTORY_SIZE: usize = 1000;

        if history.len() > MAX_HISTORY_SIZE {
            let drain_count = history.len() - MAX_HISTORY_SIZE;
            history.drain(0..drain_count);
        }
    }

    /// 获取活跃告警
    pub async fn get_active_alerts(&self) -> Vec<Alert> {
        let active_alerts = self.active_alerts.read().await;
        active_alerts.values().cloned().collect()
    }

    /// 获取告警历史
    pub async fn get_alert_history(&self, limit: Option<usize>) -> Vec<Alert> {
        let alert_history = self.alert_history.read().await;
        let limit = limit.unwrap_or(100);

        alert_history
            .iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: AlertConfig) -> Result<()> {
        let old_config = self.config.read().await.clone();

        // 检查是否需要重启
        let need_restart = old_config.enabled != new_config.enabled;

        // 更新配置
        {
            let mut config = self.config.write().await;
            *config = new_config;
        }

        if need_restart {
            info!("告警配置变更需要重启服务");
            self.stop().await?;
            self.start().await?;
        }

        Ok(())
    }
}

/// 告警
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Alert {
    /// 告警ID
    pub id: String,
    /// 告警类型
    pub alert_type: AlertType,
    /// 严重程度
    pub severity: AlertSeverity,
    /// 告警消息
    pub message: String,
    /// 当前值
    pub value: f64,
    /// 阈值
    pub threshold: f64,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 是否已解决
    pub resolved: bool,
}

/// 告警类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertType {
    /// CPU使用率过高
    CpuUsageHigh,
    /// 内存使用率过高
    MemoryUsageHigh,
    /// 磁盘使用率过高
    DiskUsageHigh,
    /// 连接数过多
    ConnectionCountHigh,
    /// 错误率过高
    ErrorRateHigh,
    /// 响应时间过长
    ResponseTimeSlow,
    /// 自定义告警
    Custom(String),
}

/// 告警严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 错误
    Error,
    /// 严重
    Critical,
}

/// 告警通知
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertNotification {
    /// 通知类型 (alert/resolved)
    pub alert_type: String,
    /// 告警信息
    pub alert: Alert,
}
