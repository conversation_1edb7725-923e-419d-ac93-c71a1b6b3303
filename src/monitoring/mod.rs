// 监控管理模块
// 提供Prometheus指标收集、结构化日志记录和性能监控

pub mod metrics;
pub mod alerts;

use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{info, error, warn};
use prometheus::{Registry, Encoder, TextEncoder};

use crate::config::MonitoringConfig;
use crate::error::{ForwarderError, Result};

/// 监控管理器
pub struct MonitoringManager {
    /// 配置
    config: Arc<RwLock<MonitoringConfig>>,
    /// Prometheus注册表
    registry: Arc<Registry>,
    /// 指标收集器
    metrics_collector: Arc<metrics::MetricsCollector>,
    /// 告警管理器
    alert_manager: Arc<alerts::AlertManager>,
    /// 指标收集任务句柄
    metrics_task: Arc<RwLock<Option<tokio::task::Join<PERSON><PERSON><PERSON><()>>>>,
    /// HTTP服务器句柄
    http_server_task: Arc<RwLock<Option<tokio::task::Join<PERSON><PERSON><PERSON><()>>>>,
}

impl MonitoringManager {
    /// 创建新的监控管理器
    pub async fn new(config: MonitoringConfig) -> Result<Self> {
        let registry = Arc::new(Registry::new());
        let metrics_collector = Arc::new(metrics::MetricsCollector::new(registry.clone())?);
        let alert_manager = Arc::new(alerts::AlertManager::new(config.alerts.clone()).await?);

        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            registry,
            metrics_collector,
            alert_manager,
            metrics_task: Arc::new(RwLock::new(None)),
            http_server_task: Arc::new(RwLock::new(None)),
        })
    }

    /// 启动监控管理器
    pub async fn start(&self) -> Result<()> {
        let config = self.config.read().await;
        
        if !config.enabled {
            info!("监控功能已禁用");
            return Ok(());
        }

        info!("启动监控管理器");

        // 启动指标收集任务
        self.start_metrics_collection_task().await;

        // 启动HTTP服务器
        self.start_http_server().await?;

        // 启动告警管理器
        self.alert_manager.start().await?;

        info!("监控管理器启动完成");
        Ok(())
    }

    /// 停止监控管理器
    pub async fn stop(&self) -> Result<()> {
        info!("停止监控管理器");

        // 停止指标收集任务
        self.stop_metrics_collection_task().await;

        // 停止HTTP服务器
        self.stop_http_server().await;

        // 停止告警管理器
        self.alert_manager.stop().await?;

        info!("监控管理器停止完成");
        Ok(())
    }

    /// 启动指标收集任务
    async fn start_metrics_collection_task(&self) {
        let mut task = self.metrics_task.write().await;
        if task.is_some() {
            warn!("指标收集任务已经在运行");
            return;
        }

        let config = self.config.clone();
        let metrics_collector = self.metrics_collector.clone();
        let alert_manager = self.alert_manager.clone();

        let collection_task = tokio::spawn(async move {
            loop {
                let interval_seconds = {
                    let config = config.read().await;
                    config.metrics_interval_seconds
                };

                let mut interval = interval(Duration::from_secs(interval_seconds));
                interval.tick().await;

                // 收集指标
                if let Err(e) = metrics_collector.collect_metrics().await {
                    error!("收集指标失败: {}", e);
                }

                // 检查告警条件
                if let Err(e) = alert_manager.check_alert_conditions(&metrics_collector).await {
                    error!("检查告警条件失败: {}", e);
                }
            }
        });

        *task = Some(collection_task);
        info!("指标收集任务已启动");
    }

    /// 停止指标收集任务
    async fn stop_metrics_collection_task(&self) {
        let mut task = self.metrics_task.write().await;
        if let Some(task_handle) = task.take() {
            task_handle.abort();
            info!("指标收集任务已停止");
        }
    }

    /// 启动HTTP服务器
    async fn start_http_server(&self) -> Result<()> {
        let config = self.config.read().await;
        let metrics_port = config.metrics_port;
        let health_port = config.health_check_port;
        let registry = self.registry.clone();

        // 启动指标服务器
        let metrics_server = tokio::spawn(async move {
            Self::run_metrics_server(metrics_port, registry).await
        });

        // 启动健康检查服务器
        let health_server = tokio::spawn(async move {
            Self::run_health_server(health_port).await
        });

        // 组合两个服务器任务
        let combined_task = tokio::spawn(async move {
            tokio::select! {
                result = metrics_server => {
                    if let Err(e) = result {
                        error!("指标服务器任务失败: {}", e);
                    }
                }
                result = health_server => {
                    if let Err(e) = result {
                        error!("健康检查服务器任务失败: {}", e);
                    }
                }
            }
        });

        let mut server_task = self.http_server_task.write().await;
        *server_task = Some(combined_task);

        info!("HTTP服务器已启动 - 指标端口: {}, 健康检查端口: {}", metrics_port, health_port);
        Ok(())
    }

    /// 停止HTTP服务器
    async fn stop_http_server(&self) {
        let mut task = self.http_server_task.write().await;
        if let Some(task_handle) = task.take() {
            task_handle.abort();
            info!("HTTP服务器已停止");
        }
    }

    /// 运行指标服务器
    async fn run_metrics_server(port: u16, registry: Arc<Registry>) {
        use axum::{
            extract::State,
            http::StatusCode,
            response::Response,
            routing::get,
            Router,
        };

        async fn metrics_handler(State(registry): State<Arc<Registry>>) -> Result<Response<String>, StatusCode> {
            let encoder = TextEncoder::new();
            let metric_families = registry.gather();
            
            match encoder.encode_to_string(&metric_families) {
                Ok(output) => {
                    Ok(Response::builder()
                        .header("content-type", "text/plain; version=0.0.4")
                        .body(output)
                        .unwrap())
                }
                Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
            }
        }

        let app = Router::new()
            .route("/metrics", get(metrics_handler))
            .with_state(registry);

        let bind_addr = format!("0.0.0.0:{}", port);
        let listener = match tokio::net::TcpListener::bind(&bind_addr).await {
            Ok(listener) => listener,
            Err(e) => {
                error!("绑定指标服务器端口失败: {}", e);
                return;
            }
        };

        info!("指标服务器监听地址: {}", bind_addr);

        if let Err(e) = axum::serve(listener, app).await {
            error!("指标服务器运行失败: {}", e);
        }
    }

    /// 运行健康检查服务器
    async fn run_health_server(port: u16) {
        use axum::{
            http::StatusCode,
            response::Json,
            routing::get,
            Router,
        };
        use serde_json::json;

        async fn health_handler() -> Result<Json<serde_json::Value>, StatusCode> {
            Ok(Json(json!({
                "status": "healthy",
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }

        let app = Router::new()
            .route("/health", get(health_handler));

        let bind_addr = format!("0.0.0.0:{}", port);
        let listener = match tokio::net::TcpListener::bind(&bind_addr).await {
            Ok(listener) => listener,
            Err(e) => {
                error!("绑定健康检查服务器端口失败: {}", e);
                return;
            }
        };

        info!("健康检查服务器监听地址: {}", bind_addr);

        if let Err(e) = axum::serve(listener, app).await {
            error!("健康检查服务器运行失败: {}", e);
        }
    }

    /// 获取指标收集器
    pub fn metrics_collector(&self) -> Arc<metrics::MetricsCollector> {
        self.metrics_collector.clone()
    }

    /// 获取告警管理器
    pub fn alert_manager(&self) -> Arc<alerts::AlertManager> {
        self.alert_manager.clone()
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: MonitoringConfig) -> Result<()> {
        let old_config = self.config.read().await.clone();
        
        // 检查是否需要重启服务
        let need_restart = old_config.enabled != new_config.enabled
            || old_config.metrics_port != new_config.metrics_port
            || old_config.health_check_port != new_config.health_check_port;

        // 更新配置
        {
            let mut config = self.config.write().await;
            *config = new_config.clone();
        }

        // 更新告警管理器配置
        self.alert_manager.update_config(new_config.alerts).await?;

        if need_restart {
            info!("监控配置变更需要重启服务");
            self.stop().await?;
            self.start().await?;
        }

        Ok(())
    }

    /// 获取当前指标
    pub async fn get_current_metrics(&self) -> Result<String> {
        let encoder = TextEncoder::new();
        let metric_families = self.registry.gather();
        
        encoder.encode_to_string(&metric_families)
            .map_err(|e| ForwarderError::InternalError(format!("编码指标失败: {}", e)))
    }

    /// 记录自定义指标
    pub async fn record_custom_metric(&self, name: &str, value: f64, labels: Vec<(&str, &str)>) -> Result<()> {
        self.metrics_collector.record_custom_metric(name, value, labels).await
    }

    /// 增加计数器
    pub async fn increment_counter(&self, name: &str, labels: Vec<(&str, &str)>) -> Result<()> {
        self.metrics_collector.increment_counter(name, labels).await
    }

    /// 记录直方图
    pub async fn record_histogram(&self, name: &str, value: f64, labels: Vec<(&str, &str)>) -> Result<()> {
        self.metrics_collector.record_histogram(name, value, labels).await
    }

    /// 设置仪表盘值
    pub async fn set_gauge(&self, name: &str, value: f64, labels: Vec<(&str, &str)>) -> Result<()> {
        self.metrics_collector.set_gauge(name, value, labels).await
    }

    /// 获取监控状态
    pub async fn get_monitoring_status(&self) -> MonitoringStatus {
        let config = self.config.read().await;
        
        MonitoringStatus {
            enabled: config.enabled,
            metrics_collection_running: self.metrics_task.read().await.is_some(),
            http_server_running: self.http_server_task.read().await.is_some(),
            metrics_port: config.metrics_port,
            health_check_port: config.health_check_port,
            metrics_interval_seconds: config.metrics_interval_seconds,
            alerts_enabled: config.alerts.enabled,
        }
    }
}

/// 监控状态
#[derive(Debug, Clone)]
pub struct MonitoringStatus {
    /// 是否启用监控
    pub enabled: bool,
    /// 指标收集是否运行
    pub metrics_collection_running: bool,
    /// HTTP服务器是否运行
    pub http_server_running: bool,
    /// 指标端口
    pub metrics_port: u16,
    /// 健康检查端口
    pub health_check_port: u16,
    /// 指标收集间隔
    pub metrics_interval_seconds: u64,
    /// 告警是否启用
    pub alerts_enabled: bool,
}
