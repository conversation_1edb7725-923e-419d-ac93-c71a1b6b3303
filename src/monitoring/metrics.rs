// Prometheus指标收集器
// 提供系统指标、业务指标和自定义指标的收集功能

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use prometheus::{
    Registry, Gauge, CounterVec, GaugeVec, HistogramVec,
    Opts, HistogramOpts, register_gauge_with_registry,
    register_counter_vec_with_registry,
    register_gauge_vec_with_registry, register_histogram_vec_with_registry,
};
use tracing::{debug, error, warn};
use sysinfo::{System, SystemExt, CpuExt, ProcessExt, DiskExt, NetworkExt};

use crate::error::{ForwarderError, Result};

/// 指标收集器
pub struct MetricsCollector {
    /// Prometheus注册表
    registry: Arc<Registry>,
    /// 系统信息收集器
    system: Arc<RwLock<System>>,
    /// 启动时间
    start_time: Instant,

    // 系统指标
    /// CPU使用率
    cpu_usage_gauge: Gauge,
    /// 内存使用率
    memory_usage_gauge: Gauge,
    /// 磁盘使用率
    disk_usage_gauge_vec: GaugeVec,
    /// 网络流量
    network_bytes_counter_vec: CounterVec,

    // 业务指标
    /// 活跃连接数
    active_connections_gauge: Gauge,
    /// 活跃会话数
    active_sessions_gauge: Gauge,
    /// 活跃传输数
    active_transports_gauge: Gauge,
    /// 请求计数器
    requests_counter_vec: CounterVec,
    /// 请求延迟直方图
    request_duration_histogram_vec: HistogramVec,
    /// 错误计数器
    errors_counter_vec: CounterVec,

    // 自定义指标存储
    custom_counters: Arc<RwLock<HashMap<String, CounterVec>>>,
    custom_gauges: Arc<RwLock<HashMap<String, GaugeVec>>>,
    custom_histograms: Arc<RwLock<HashMap<String, HistogramVec>>>,
}

impl MetricsCollector {
    /// 创建新的指标收集器
    pub fn new(registry: Arc<Registry>) -> Result<Self> {
        let mut system = System::new_all();
        system.refresh_all();

        // 创建系统指标
        let cpu_usage_gauge = register_gauge_with_registry!(
            Opts::new("system_cpu_usage_percent", "系统CPU使用率百分比"),
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册CPU指标失败: {}", e)))?;

        let memory_usage_gauge = register_gauge_with_registry!(
            Opts::new("system_memory_usage_percent", "系统内存使用率百分比"),
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册内存指标失败: {}", e)))?;

        let disk_usage_gauge_vec = register_gauge_vec_with_registry!(
            Opts::new("system_disk_usage_percent", "系统磁盘使用率百分比"),
            &["mount_point"],
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册磁盘指标失败: {}", e)))?;

        let network_bytes_counter_vec = register_counter_vec_with_registry!(
            Opts::new("system_network_bytes_total", "网络流量字节数"),
            &["interface", "direction"],
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册网络指标失败: {}", e)))?;

        // 创建业务指标
        let active_connections_gauge = register_gauge_with_registry!(
            Opts::new("kvm_tunnel_active_connections", "活跃连接数"),
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册连接指标失败: {}", e)))?;

        let active_sessions_gauge = register_gauge_with_registry!(
            Opts::new("kvm_tunnel_active_sessions", "活跃会话数"),
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册会话指标失败: {}", e)))?;

        let active_transports_gauge = register_gauge_with_registry!(
            Opts::new("kvm_tunnel_active_transports", "活跃传输数"),
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册传输指标失败: {}", e)))?;

        let requests_counter_vec = register_counter_vec_with_registry!(
            Opts::new("kvm_tunnel_requests_total", "请求总数"),
            &["method", "endpoint", "status"],
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册请求指标失败: {}", e)))?;

        let request_duration_histogram_vec = register_histogram_vec_with_registry!(
            HistogramOpts::new("kvm_tunnel_request_duration_seconds", "请求处理时间")
                .buckets(vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0]),
            &["method", "endpoint"],
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册请求延迟指标失败: {}", e)))?;

        let errors_counter_vec = register_counter_vec_with_registry!(
            Opts::new("kvm_tunnel_errors_total", "错误总数"),
            &["type", "component"],
            &*registry
        ).map_err(|e| ForwarderError::InternalError(format!("注册错误指标失败: {}", e)))?;

        Ok(Self {
            registry,
            system: Arc::new(RwLock::new(system)),
            start_time: Instant::now(),
            cpu_usage_gauge,
            memory_usage_gauge,
            disk_usage_gauge_vec,
            network_bytes_counter_vec,
            active_connections_gauge,
            active_sessions_gauge,
            active_transports_gauge,
            requests_counter_vec,
            request_duration_histogram_vec,
            errors_counter_vec,
            custom_counters: Arc::new(RwLock::new(HashMap::new())),
            custom_gauges: Arc::new(RwLock::new(HashMap::new())),
            custom_histograms: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    /// 收集所有指标
    pub async fn collect_metrics(&self) -> Result<()> {
        debug!("开始收集指标");

        // 收集系统指标
        self.collect_system_metrics().await?;

        // 收集业务指标
        self.collect_business_metrics().await?;

        debug!("指标收集完成");
        Ok(())
    }

    /// 收集系统指标
    async fn collect_system_metrics(&self) -> Result<()> {
        let mut system = self.system.write().await;
        system.refresh_all();

        // CPU使用率
        let cpu_usage = system.global_cpu_info().cpu_usage() as f64;
        self.cpu_usage_gauge.set(cpu_usage);

        // 内存使用率
        let total_memory = system.total_memory() as f64;
        let used_memory = system.used_memory() as f64;
        let memory_usage = if total_memory > 0.0 {
            (used_memory / total_memory) * 100.0
        } else {
            0.0
        };
        self.memory_usage_gauge.set(memory_usage);

        // 磁盘使用率
        for disk in system.disks() {
            let mount_point = disk.mount_point().to_string_lossy();
            let total_space = disk.total_space() as f64;
            let available_space = disk.available_space() as f64;
            let used_space = total_space - available_space;
            let usage_percent = if total_space > 0.0 {
                (used_space / total_space) * 100.0
            } else {
                0.0
            };

            self.disk_usage_gauge_vec
                .with_label_values(&[&mount_point])
                .set(usage_percent);
        }

        // 网络流量
        for (interface_name, data) in system.networks() {
            self.network_bytes_counter_vec
                .with_label_values(&[interface_name, "received"])
                .inc_by(data.received() as f64);

            self.network_bytes_counter_vec
                .with_label_values(&[interface_name, "transmitted"])
                .inc_by(data.transmitted() as f64);
        }

        Ok(())
    }

    /// 收集业务指标
    async fn collect_business_metrics(&self) -> Result<()> {
        // TODO: 这里需要从实际的管理器中获取数据
        // 目前使用模拟数据

        // 模拟活跃连接数
        self.active_connections_gauge.set(10.0);

        // 模拟活跃会话数
        self.active_sessions_gauge.set(5.0);

        // 模拟活跃传输数
        self.active_transports_gauge.set(3.0);

        Ok(())
    }

    /// 记录HTTP请求
    pub fn record_http_request(&self, method: &str, endpoint: &str, status: u16, duration: Duration) {
        // 记录请求计数
        let status_str = status.to_string();
        self.requests_counter_vec
            .with_label_values(&[method, endpoint, &status_str])
            .inc();

        // 记录请求延迟
        self.request_duration_histogram_vec
            .with_label_values(&[method, endpoint])
            .observe(duration.as_secs_f64());
    }

    /// 记录错误
    pub fn record_error(&self, error_type: &str, component: &str) {
        self.errors_counter_vec
            .with_label_values(&[error_type, component])
            .inc();
    }

    /// 设置活跃连接数
    pub fn set_active_connections(&self, count: f64) {
        self.active_connections_gauge.set(count);
    }

    /// 设置活跃会话数
    pub fn set_active_sessions(&self, count: f64) {
        self.active_sessions_gauge.set(count);
    }

    /// 设置活跃传输数
    pub fn set_active_transports(&self, count: f64) {
        self.active_transports_gauge.set(count);
    }

    /// 记录自定义指标
    pub async fn record_custom_metric(&self, name: &str, value: f64, labels: Vec<(&str, &str)>) -> Result<()> {
        let gauge_name = format!("custom_{}", name);
        let mut custom_gauges = self.custom_gauges.write().await;

        // 获取或创建自定义仪表盘
        let gauge = if let Some(gauge) = custom_gauges.get(&gauge_name) {
            gauge.clone()
        } else {
            let label_names: Vec<&str> = labels.iter().map(|(k, _)| *k).collect();
            let gauge = register_gauge_vec_with_registry!(
                Opts::new(&gauge_name, &format!("自定义指标: {}", name)),
                &label_names,
                &*self.registry
            ).map_err(|e| ForwarderError::InternalError(format!("注册自定义指标失败: {}", e)))?;

            custom_gauges.insert(gauge_name.clone(), gauge.clone());
            gauge
        };

        // 设置指标值
        let label_values: Vec<&str> = labels.iter().map(|(_, v)| *v).collect();
        gauge.with_label_values(&label_values).set(value);

        Ok(())
    }

    /// 增加计数器
    pub async fn increment_counter(&self, name: &str, labels: Vec<(&str, &str)>) -> Result<()> {
        let counter_name = format!("custom_{}_total", name);
        let mut custom_counters = self.custom_counters.write().await;

        // 获取或创建自定义计数器
        let counter = if let Some(counter) = custom_counters.get(&counter_name) {
            counter.clone()
        } else {
            let label_names: Vec<&str> = labels.iter().map(|(k, _)| *k).collect();
            let counter = register_counter_vec_with_registry!(
                Opts::new(&counter_name, &format!("自定义计数器: {}", name)),
                &label_names,
                &*self.registry
            ).map_err(|e| ForwarderError::InternalError(format!("注册自定义计数器失败: {}", e)))?;

            custom_counters.insert(counter_name.clone(), counter.clone());
            counter
        };

        // 增加计数
        let label_values: Vec<&str> = labels.iter().map(|(_, v)| *v).collect();
        counter.with_label_values(&label_values).inc();

        Ok(())
    }

    /// 记录直方图
    pub async fn record_histogram(&self, name: &str, value: f64, labels: Vec<(&str, &str)>) -> Result<()> {
        let histogram_name = format!("custom_{}_seconds", name);
        let mut custom_histograms = self.custom_histograms.write().await;

        // 获取或创建自定义直方图
        let histogram = if let Some(histogram) = custom_histograms.get(&histogram_name) {
            histogram.clone()
        } else {
            let label_names: Vec<&str> = labels.iter().map(|(k, _)| *k).collect();
            let histogram = register_histogram_vec_with_registry!(
                HistogramOpts::new(&histogram_name, &format!("自定义直方图: {}", name))
                    .buckets(vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0]),
                &label_names,
                &*self.registry
            ).map_err(|e| ForwarderError::InternalError(format!("注册自定义直方图失败: {}", e)))?;

            custom_histograms.insert(histogram_name.clone(), histogram.clone());
            histogram
        };

        // 记录值
        let label_values: Vec<&str> = labels.iter().map(|(_, v)| *v).collect();
        histogram.with_label_values(&label_values).observe(value);

        Ok(())
    }

    /// 设置仪表盘值
    pub async fn set_gauge(&self, name: &str, value: f64, labels: Vec<(&str, &str)>) -> Result<()> {
        self.record_custom_metric(name, value, labels).await
    }

    /// 获取运行时间
    pub fn get_uptime_seconds(&self) -> f64 {
        self.start_time.elapsed().as_secs_f64()
    }

    /// 获取系统信息摘要
    pub async fn get_system_summary(&self) -> SystemSummary {
        let system = self.system.read().await;

        SystemSummary {
            cpu_usage_percent: system.global_cpu_info().cpu_usage() as f64,
            memory_usage_percent: {
                let total = system.total_memory() as f64;
                let used = system.used_memory() as f64;
                if total > 0.0 { (used / total) * 100.0 } else { 0.0 }
            },
            disk_usage_percent: {
                let mut total_space = 0u64;
                let mut used_space = 0u64;
                for disk in system.disks() {
                    total_space += disk.total_space();
                    used_space += disk.total_space() - disk.available_space();
                }
                if total_space > 0 {
                    (used_space as f64 / total_space as f64) * 100.0
                } else {
                    0.0
                }
            },
            uptime_seconds: self.get_uptime_seconds(),
            process_count: system.processes().len(),
        }
    }
}

/// 系统信息摘要
#[derive(Debug, Clone)]
pub struct SystemSummary {
    /// CPU使用率百分比
    pub cpu_usage_percent: f64,
    /// 内存使用率百分比
    pub memory_usage_percent: f64,
    /// 磁盘使用率百分比
    pub disk_usage_percent: f64,
    /// 运行时间（秒）
    pub uptime_seconds: f64,
    /// 进程数量
    pub process_count: usize,
}
