// 结构化日志系统
// 提供统一的日志记录、日志轮转和日志分析功能

use std::collections::HashMap;
use std::fs::OpenOptions;
use std::io::Write;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{info, warn, error, debug};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::config::{LoggingConfig, LogFormat, LogFileConfig};
use crate::error::{ForwarderError, Result};

/// 日志管理器
pub struct LogManager {
    /// 日志配置
    config: Arc<RwLock<LoggingConfig>>,
    /// 日志文件句柄
    log_files: Arc<RwLock<HashMap<String, LogFile>>>,
    /// 日志轮转任务句柄
    rotation_task: Arc<RwLock<Option<tokio::task::Jo<PERSON><PERSON><PERSON><PERSON><()>>>>,
    /// 日志统计
    log_stats: Arc<RwLock<LogStatistics>>,
}

impl LogManager {
    /// 创建新的日志管理器
    pub async fn new(config: LoggingConfig) -> Result<Self> {
        // 确保日志目录存在
        if let Some(file_config) = &config.file {
            if let Some(parent) = Path::new(&file_config.path).parent() {
                tokio::fs::create_dir_all(parent).await
                    .map_err(|e| ForwarderError::InternalError(format!("创建日志目录失败: {}", e)))?;
            }
        }

        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            log_files: Arc::new(RwLock::new(HashMap::new())),
            rotation_task: Arc::new(RwLock::new(None)),
            log_stats: Arc::new(RwLock::new(LogStatistics::new())),
        })
    }

    /// 启动日志管理器
    pub async fn start(&self) -> Result<()> {
        info!("启动日志管理器");
        
        // 启动日志轮转任务
        self.start_rotation_task().await;
        
        info!("日志管理器启动完成");
        Ok(())
    }

    /// 停止日志管理器
    pub async fn stop(&self) -> Result<()> {
        info!("停止日志管理器");
        
        // 停止日志轮转任务
        self.stop_rotation_task().await;
        
        // 关闭所有日志文件
        self.close_all_log_files().await;
        
        info!("日志管理器停止完成");
        Ok(())
    }

    /// 启动日志轮转任务
    async fn start_rotation_task(&self) {
        let mut task = self.rotation_task.write().await;
        if task.is_some() {
            warn!("日志轮转任务已经在运行");
            return;
        }

        let config = self.config.clone();
        let log_files = self.log_files.clone();

        let rotation_task = tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(3600)); // 每小时检查一次
            
            loop {
                interval.tick().await;
                
                let config = config.read().await;
                if !config.rotation.enabled {
                    continue;
                }
                
                // 检查并执行日志轮转
                if let Err(e) = Self::check_and_rotate_logs(&config, &log_files).await {
                    error!("日志轮转失败: {}", e);
                }
                
                // 清理旧日志文件
                if let Err(e) = Self::cleanup_old_logs(&config).await {
                    error!("清理旧日志失败: {}", e);
                }
            }
        });

        *task = Some(rotation_task);
        info!("日志轮转任务已启动");
    }

    /// 停止日志轮转任务
    async fn stop_rotation_task(&self) {
        let mut task = self.rotation_task.write().await;
        if let Some(task_handle) = task.take() {
            task_handle.abort();
            info!("日志轮转任务已停止");
        }
    }

    /// 写入日志条目
    pub async fn write_log_entry(&self, entry: LogEntry) -> Result<()> {
        let config = self.config.read().await;

        // 检查日志级别
        if !self.should_log(&entry.level, &config.level) {
            return Ok(());
        }

        // 格式化日志条目
        let formatted = self.format_log_entry(&entry, &config).await?;

        // 写入到文件
        if let Some(file_config) = &config.file {
            self.write_to_file(&formatted, &file_config.path).await?;
        }

        // 更新统计
        self.update_log_stats(&entry).await;

        Ok(())
    }

    /// 检查是否应该记录日志
    fn should_log(&self, entry_level: &LogLevel, config_level: &str) -> bool {
        let config_priority = match config_level.to_lowercase().as_str() {
            "error" => 4,
            "warn" => 3,
            "info" => 2,
            "debug" => 1,
            "trace" => 0,
            _ => 2, // 默认为info级别
        };
        entry_level.as_priority() >= config_priority
    }

    /// 格式化日志条目
    async fn format_log_entry(&self, entry: &LogEntry, config: &LoggingConfig) -> Result<String> {
        match config.format {
            LogFormat::Json => {
                serde_json::to_string(entry)
                    .map_err(|e| ForwarderError::InternalError(format!("序列化日志条目失败: {}", e)))
            }
            LogFormat::Text => {
                Ok(format!(
                    "{} [{}] {} - {} ({}:{})\n",
                    entry.timestamp.format("%Y-%m-%d %H:%M:%S%.3f"),
                    entry.level,
                    entry.target,
                    entry.message,
                    entry.file.as_deref().unwrap_or("unknown"),
                    entry.line.unwrap_or(0)
                ))
            }
        }
    }

    /// 写入到文件
    async fn write_to_file(&self, content: &str, file_path: &str) -> Result<()> {
        let mut log_files = self.log_files.write().await;
        
        // 获取或创建日志文件
        let log_file = if let Some(file) = log_files.get_mut(file_path) {
            file
        } else {
            let new_file = LogFile::new(file_path).await?;
            log_files.insert(file_path.to_string(), new_file);
            log_files.get_mut(file_path).unwrap()
        };
        
        // 写入内容
        log_file.write(content).await?;
        
        Ok(())
    }

    /// 更新日志统计
    async fn update_log_stats(&self, entry: &LogEntry) {
        let mut stats = self.log_stats.write().await;
        stats.total_entries += 1;
        
        match entry.level {
            LogLevel::Error => stats.error_count += 1,
            LogLevel::Warn => stats.warn_count += 1,
            LogLevel::Info => stats.info_count += 1,
            LogLevel::Debug => stats.debug_count += 1,
            LogLevel::Trace => stats.trace_count += 1,
        }
        
        stats.last_entry_time = Some(entry.timestamp);
    }

    /// 检查并执行日志轮转
    async fn check_and_rotate_logs(
        config: &LoggingConfig,
        log_files: &Arc<RwLock<HashMap<String, LogFile>>>,
    ) -> Result<()> {
        let mut files = log_files.write().await;

        if let Some(file_config) = &config.file {
            for (path, log_file) in files.iter_mut() {
                if log_file.should_rotate(file_config).await? {
                    info!("轮转日志文件: {}", path);
                    log_file.rotate(file_config).await?;
                }
            }
        }

        Ok(())
    }

    /// 清理旧日志文件
    async fn cleanup_old_logs(config: &LoggingConfig) -> Result<()> {
        let file_config = match &config.file {
            Some(fc) => fc,
            None => return Ok(()),
        };

        if file_config.max_files == 0 {
            return Ok(());
        }

        let log_dir = Path::new(&file_config.path).parent()
            .ok_or_else(|| ForwarderError::InternalError("无法获取日志目录".to_string()))?;
        
        let mut entries = tokio::fs::read_dir(log_dir).await
            .map_err(|e| ForwarderError::InternalError(format!("读取日志目录失败: {}", e)))?;
        
        let mut log_files = Vec::new();
        
        while let Some(entry) = entries.next_entry().await
            .map_err(|e| ForwarderError::InternalError(format!("读取目录条目失败: {}", e)))? {
            
            let path = entry.path();
            if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                if name.starts_with("kvm_tunnel") && name.ends_with(".log") {
                    if let Ok(metadata) = entry.metadata().await {
                        if let Ok(modified) = metadata.modified() {
                            log_files.push((path, modified));
                        }
                    }
                }
            }
        }
        
        // 按修改时间排序，保留最新的文件
        log_files.sort_by(|a, b| b.1.cmp(&a.1));

        // 删除超出限制的文件
        for (path, _) in log_files.iter().skip(file_config.max_files) {
            if let Err(e) = tokio::fs::remove_file(path).await {
                warn!("删除旧日志文件失败: {} - {}", path.display(), e);
            } else {
                info!("删除旧日志文件: {}", path.display());
            }
        }
        
        Ok(())
    }

    /// 关闭所有日志文件
    async fn close_all_log_files(&self) {
        let mut log_files = self.log_files.write().await;
        for (path, log_file) in log_files.drain() {
            if let Err(e) = log_file.close().await {
                error!("关闭日志文件失败: {} - {}", path, e);
            }
        }
    }

    /// 获取日志统计
    pub async fn get_log_statistics(&self) -> LogStatistics {
        self.log_stats.read().await.clone()
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: LoggingConfig) -> Result<()> {
        let old_config = self.config.read().await.clone();

        // 检查是否需要重启
        let need_restart = match (&old_config.file, &new_config.file) {
            (Some(old_file), Some(new_file)) => {
                old_file.path != new_file.path
            }
            (None, Some(_)) | (Some(_), None) => true,
            (None, None) => false,
        };

        // 更新配置
        {
            let mut config = self.config.write().await;
            *config = new_config;
        }

        if need_restart {
            info!("日志配置变更需要重启服务");
            self.stop().await?;
            self.start().await?;
        }

        Ok(())
    }
}

/// 日志文件
struct LogFile {
    /// 文件路径
    path: PathBuf,
    /// 文件句柄
    file: Option<std::fs::File>,
    /// 当前文件大小
    size: u64,
    /// 创建时间
    created_at: SystemTime,
}

impl LogFile {
    /// 创建新的日志文件
    async fn new(path: &str) -> Result<Self> {
        let path_buf = PathBuf::from(path);

        // 确保父目录存在
        if let Some(parent) = path_buf.parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| ForwarderError::InternalError(format!("创建日志目录失败: {}", e)))?;
        }

        let file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&path_buf)
            .map_err(|e| ForwarderError::InternalError(format!("打开日志文件失败: {}", e)))?;

        let metadata = file.metadata()
            .map_err(|e| ForwarderError::InternalError(format!("获取文件元数据失败: {}", e)))?;

        Ok(Self {
            path: path_buf,
            file: Some(file),
            size: metadata.len(),
            created_at: metadata.created().unwrap_or(UNIX_EPOCH),
        })
    }

    /// 写入内容
    async fn write(&mut self, content: &str) -> Result<()> {
        if let Some(ref mut file) = self.file {
            file.write_all(content.as_bytes())
                .map_err(|e| ForwarderError::InternalError(format!("写入日志失败: {}", e)))?;

            file.flush()
                .map_err(|e| ForwarderError::InternalError(format!("刷新日志失败: {}", e)))?;

            self.size += content.len() as u64;
        }

        Ok(())
    }

    /// 检查是否需要轮转
    async fn should_rotate(&self, config: &LogFileConfig) -> Result<bool> {
        // 检查文件大小 (配置中是MB，需要转换为字节)
        let max_size_bytes = config.max_size_mb * 1024 * 1024;
        if self.size >= max_size_bytes {
            return Ok(true);
        }

        // 简单的时间检查 - 如果文件超过24小时就轮转
        let elapsed = self.created_at.elapsed()
            .map_err(|e| ForwarderError::InternalError(format!("计算时间差失败: {}", e)))?;

        if elapsed >= Duration::from_secs(24 * 3600) {
            return Ok(true);
        }

        Ok(false)
    }

    /// 执行轮转
    async fn rotate(&mut self, config: &LogFileConfig) -> Result<()> {
        // 关闭当前文件
        self.file = None;

        // 生成轮转文件名
        let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
        let rotated_path = self.path.with_extension(format!("log.{}", timestamp));

        // 重命名当前文件
        tokio::fs::rename(&self.path, &rotated_path).await
            .map_err(|e| ForwarderError::InternalError(format!("重命名日志文件失败: {}", e)))?;

        // 创建新文件
        let new_file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&self.path)
            .map_err(|e| ForwarderError::InternalError(format!("创建新日志文件失败: {}", e)))?;

        self.file = Some(new_file);
        self.size = 0;
        self.created_at = SystemTime::now();

        info!("日志文件轮转完成: {} -> {}", self.path.display(), rotated_path.display());

        Ok(())
    }

    /// 关闭文件
    async fn close(mut self) -> Result<()> {
        if let Some(mut file) = self.file.take() {
            file.flush()
                .map_err(|e| ForwarderError::InternalError(format!("刷新日志失败: {}", e)))?;
        }
        Ok(())
    }
}

/// 日志条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 日志级别
    pub level: LogLevel,
    /// 目标模块
    pub target: String,
    /// 日志消息
    pub message: String,
    /// 源文件
    pub file: Option<String>,
    /// 行号
    pub line: Option<u32>,
    /// 额外字段
    pub fields: HashMap<String, serde_json::Value>,
}

/// 日志级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogLevel {
    Error,
    Warn,
    Info,
    Debug,
    Trace,
}

impl LogLevel {
    /// 获取优先级
    fn as_priority(&self) -> u8 {
        match self {
            LogLevel::Error => 4,
            LogLevel::Warn => 3,
            LogLevel::Info => 2,
            LogLevel::Debug => 1,
            LogLevel::Trace => 0,
        }
    }
}

impl std::fmt::Display for LogLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            LogLevel::Error => write!(f, "ERROR"),
            LogLevel::Warn => write!(f, "WARN"),
            LogLevel::Info => write!(f, "INFO"),
            LogLevel::Debug => write!(f, "DEBUG"),
            LogLevel::Trace => write!(f, "TRACE"),
        }
    }
}

/// 日志统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogStatistics {
    /// 总条目数
    pub total_entries: u64,
    /// 错误数量
    pub error_count: u64,
    /// 警告数量
    pub warn_count: u64,
    /// 信息数量
    pub info_count: u64,
    /// 调试数量
    pub debug_count: u64,
    /// 追踪数量
    pub trace_count: u64,
    /// 最后条目时间
    pub last_entry_time: Option<DateTime<Utc>>,
}

impl LogStatistics {
    fn new() -> Self {
        Self {
            total_entries: 0,
            error_count: 0,
            warn_count: 0,
            info_count: 0,
            debug_count: 0,
            trace_count: 0,
            last_entry_time: None,
        }
    }
}
