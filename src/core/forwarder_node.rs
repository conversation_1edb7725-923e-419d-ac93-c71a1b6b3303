// 转发节点主入口
// 协调所有组件的运行

use std::sync::Arc;
use tracing::{info, error};

use crate::config::ForwarderConfig;
use crate::error::{ForwarderError, Result};
use crate::core::CoreManager;

/// 转发节点
pub struct ForwarderNode {
    /// 核心管理器
    core_manager: Arc<CoreManager>,
}

impl ForwarderNode {
    /// 创建新的转发节点
    pub async fn new(config: ForwarderConfig) -> Result<Self> {
        info!("初始化转发节点");

        let core_manager = Arc::new(CoreManager::new(config).await?);

        Ok(Self {
            core_manager,
        })
    }

    /// 启动转发节点
    pub async fn start(&self) -> Result<()> {
        info!("启动转发节点");

        // 启动核心管理器
        self.core_manager.start().await?;

        info!("转发节点启动完成");
        Ok(())
    }

    /// 停止转发节点
    pub async fn stop(&self) -> Result<()> {
        info!("停止转发节点");

        // 停止核心管理器
        self.core_manager.stop().await?;

        info!("转发节点停止完成");
        Ok(())
    }

    /// 获取核心管理器
    pub fn core_manager(&self) -> Arc<CoreManager> {
        self.core_manager.clone()
    }

    /// 获取节点状态
    pub async fn get_status(&self) -> crate::types::NodeStatus {
        self.core_manager.node_manager().get_status().await
    }

    /// 获取节点信息
    pub async fn get_node_info(&self) -> crate::types::NodeInfo {
        self.core_manager.node_manager().get_node_info().await
    }

    /// 获取健康状态
    pub async fn get_health(&self) -> crate::core::HealthStatus {
        self.core_manager.get_health_status().await
    }

    /// 获取系统统计
    pub async fn get_stats(&self) -> crate::core::SystemStats {
        self.core_manager.get_system_stats().await
    }
}
