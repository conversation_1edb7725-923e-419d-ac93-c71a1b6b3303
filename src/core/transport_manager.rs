// 传输管理器实现
// 负责管理各种传输类型：MediaSoup、TCP/UDP隧道、组播等

use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use tokio::time::{interval, Duration};
use async_trait::async_trait;
use tracing::{info, warn, error, debug};

use crate::config::MediaSoupConfig;
use crate::error::{TransportError, TransportResult};
use crate::storage::{StorageManager, TransportStore};
use crate::types::{
    TransportId, TransportInfo, TransportType, TransportStatus, TransportConfig,
    TransportStats, AddressConfig, PortRange, Protocol, generate_id,
};

/// 传输管理器
pub struct TransportManager {
    /// MediaSoup配置
    config: MediaSoupConfig,

    /// 存储管理器
    storage_manager: Arc<StorageManager>,

    /// 活跃传输映射
    active_transports: Arc<RwLock<HashMap<TransportId, Arc<dyn Transport>>>>,

    /// 传输工厂
    transport_factory: Arc<TransportFactory>,

    /// 运行状态
    running: Arc<RwLock<bool>>,

    /// 监控任务句柄
    monitor_task_handle: Arc<RwLock<Option<tokio::task::JoinHandle<()>>>>,
}

/// 传输接口
#[async_trait]
pub trait Transport: Send + Sync {
    /// 获取传输ID
    fn transport_id(&self) -> &TransportId;

    /// 获取传输类型
    fn transport_type(&self) -> TransportType;

    /// 获取传输状态
    async fn status(&self) -> TransportStatus;

    /// 启动传输
    async fn start(&self) -> TransportResult<()>;

    /// 停止传输
    async fn stop(&self) -> TransportResult<()>;

    /// 暂停传输
    async fn pause(&self) -> TransportResult<()>;

    /// 恢复传输
    async fn resume(&self) -> TransportResult<()>;

    /// 获取统计信息
    async fn get_stats(&self) -> TransportStats;

    /// 更新配置
    async fn update_config(&self, config: TransportConfig) -> TransportResult<()>;
}

/// 传输工厂
pub struct TransportFactory {
    /// MediaSoup配置
    mediasoup_config: MediaSoupConfig,
}

impl TransportManager {
    /// 创建新的传输管理器
    pub async fn new(
        config: MediaSoupConfig,
        storage_manager: Arc<StorageManager>,
    ) -> TransportResult<Self> {
        let transport_factory = Arc::new(TransportFactory::new(config.clone()));

        Ok(Self {
            config,
            storage_manager,
            active_transports: Arc::new(RwLock::new(HashMap::new())),
            transport_factory,
            running: Arc::new(RwLock::new(false)),
            monitor_task_handle: Arc::new(RwLock::new(None)),
        })
    }

    /// 启动传输管理器
    pub async fn start(&self) -> TransportResult<()> {
        let mut running = self.running.write().await;
        if *running {
            return Ok(());
        }

        *running = true;
        drop(running);

        // 启动监控任务
        self.start_monitor_task().await;

        // 恢复之前的传输
        self.restore_transports().await?;

        info!("传输管理器启动成功");
        Ok(())
    }

    /// 停止传输管理器
    pub async fn stop(&self) -> TransportResult<()> {
        let mut running = self.running.write().await;
        if !*running {
            return Ok(());
        }

        *running = false;
        drop(running);

        // 停止监控任务
        self.stop_monitor_task().await;

        // 停止所有传输
        self.stop_all_transports().await?;

        info!("传输管理器停止成功");
        Ok(())
    }

    /// 创建传输
    pub async fn create_transport(
        &self,
        transport_type: TransportType,
        config: TransportConfig,
    ) -> TransportResult<TransportId> {
        let transport_id = generate_id();

        // 创建传输实例
        let transport = self.transport_factory.create_transport(
            transport_id.clone(),
            transport_type.clone(),
            config.clone(),
        ).await?;

        // 创建传输信息
        let transport_info = TransportInfo {
            transport_id: transport_id.clone(),
            transport_type: transport_type.clone(),
            status: TransportStatus::Initializing,
            config: config.clone(),
            created_at: chrono::Utc::now(),
            stats: TransportStats::default(),
        };

        // 存储传输信息
        self.storage_manager.store_transport(transport_id.clone(), transport_info).await
            .map_err(|e| TransportError::CreationFailed {
                transport_type: format!("{:?}", transport_type),
                reason: e.to_string(),
            })?;

        // 添加到活跃传输列表
        {
            let mut active_transports = self.active_transports.write().await;
            active_transports.insert(transport_id.clone(), transport);
        }

        info!("传输创建成功: {} (类型: {:?})", transport_id, transport_type);
        Ok(transport_id)
    }

    /// 启动传输
    pub async fn start_transport(&self, transport_id: &TransportId) -> TransportResult<()> {
        let active_transports = self.active_transports.read().await;
        let transport = active_transports.get(transport_id)
            .ok_or_else(|| TransportError::TransportNotFound {
                transport_id: transport_id.clone(),
            })?;

        transport.start().await?;

        // 更新存储中的状态
        if let Ok(Some(mut transport_info)) = self.storage_manager.get_transport(transport_id).await {
            transport_info.status = TransportStatus::Active;
            let _ = self.storage_manager.store_transport(transport_id.clone(), transport_info).await;
        }

        info!("传输启动成功: {}", transport_id);
        Ok(())
    }

    /// 停止传输
    pub async fn stop_transport(&self, transport_id: &TransportId) -> TransportResult<()> {
        let active_transports = self.active_transports.read().await;
        let transport = active_transports.get(transport_id)
            .ok_or_else(|| TransportError::TransportNotFound {
                transport_id: transport_id.clone(),
            })?;

        transport.stop().await?;

        // 更新存储中的状态
        if let Ok(Some(mut transport_info)) = self.storage_manager.get_transport(transport_id).await {
            transport_info.status = TransportStatus::Closed;
            let _ = self.storage_manager.store_transport(transport_id.clone(), transport_info).await;
        }

        info!("传输停止成功: {}", transport_id);
        Ok(())
    }

    /// 删除传输
    pub async fn remove_transport(&self, transport_id: &TransportId) -> TransportResult<()> {
        // 先停止传输
        if let Err(e) = self.stop_transport(transport_id).await {
            warn!("停止传输时出错: {}", e);
        }

        // 从活跃传输列表中移除
        {
            let mut active_transports = self.active_transports.write().await;
            active_transports.remove(transport_id);
        }

        // 从存储中删除
        let _ = self.storage_manager.remove_transport(transport_id).await;

        info!("传输删除成功: {}", transport_id);
        Ok(())
    }

    /// 获取传输信息
    pub async fn get_transport_info(&self, transport_id: &TransportId) -> TransportResult<TransportInfo> {
        self.storage_manager.get_transport(transport_id).await
            .map_err(|e| TransportError::TransportNotFound {
                transport_id: transport_id.clone(),
            })?
            .ok_or_else(|| TransportError::TransportNotFound {
                transport_id: transport_id.clone(),
            })
    }

    /// 列出所有传输
    pub async fn list_transports(&self) -> TransportResult<Vec<TransportInfo>> {
        self.storage_manager.list_transports().await
            .map_err(|e| TransportError::CreationFailed {
                transport_type: "list".to_string(),
                reason: e.to_string(),
            })
    }

    /// 获取传输统计信息
    pub async fn get_transport_stats(&self, transport_id: &TransportId) -> TransportResult<TransportStats> {
        let active_transports = self.active_transports.read().await;
        let transport = active_transports.get(transport_id)
            .ok_or_else(|| TransportError::TransportNotFound {
                transport_id: transport_id.clone(),
            })?;

        Ok(transport.get_stats().await)
    }

    /// 获取传输数量
    pub async fn transport_count(&self) -> usize {
        self.storage_manager.transport_count().await
    }

    /// 按类型获取传输列表
    pub async fn get_transports_by_type(&self, transport_type: TransportType) -> TransportResult<Vec<TransportInfo>> {
        let all_transports = self.list_transports().await?;
        Ok(all_transports.into_iter()
            .filter(|t| t.transport_type == transport_type)
            .collect())
    }

    /// 按状态获取传输列表
    pub async fn get_transports_by_status(&self, status: TransportStatus) -> TransportResult<Vec<TransportInfo>> {
        let all_transports = self.list_transports().await?;
        Ok(all_transports.into_iter()
            .filter(|t| t.status == status)
            .collect())
    }

    /// 暂停传输
    pub async fn pause_transport(&self, transport_id: &TransportId) -> TransportResult<()> {
        let active_transports = self.active_transports.read().await;
        let transport = active_transports.get(transport_id)
            .ok_or_else(|| TransportError::TransportNotFound {
                transport_id: transport_id.clone(),
            })?;

        transport.pause().await?;

        // 更新存储中的状态
        if let Ok(Some(mut transport_info)) = self.storage_manager.get_transport(transport_id).await {
            transport_info.status = TransportStatus::Paused;
            let _ = self.storage_manager.store_transport(transport_id.clone(), transport_info).await;
        }

        info!("传输暂停成功: {}", transport_id);
        Ok(())
    }

    /// 恢复传输
    pub async fn resume_transport(&self, transport_id: &TransportId) -> TransportResult<()> {
        let active_transports = self.active_transports.read().await;
        let transport = active_transports.get(transport_id)
            .ok_or_else(|| TransportError::TransportNotFound {
                transport_id: transport_id.clone(),
            })?;

        transport.resume().await?;

        // 更新存储中的状态
        if let Ok(Some(mut transport_info)) = self.storage_manager.get_transport(transport_id).await {
            transport_info.status = TransportStatus::Active;
            let _ = self.storage_manager.store_transport(transport_id.clone(), transport_info).await;
        }

        info!("传输恢复成功: {}", transport_id);
        Ok(())
    }

    /// 启动监控任务
    async fn start_monitor_task(&self) {
        let storage_manager = Arc::clone(&self.storage_manager);
        let active_transports = Arc::clone(&self.active_transports);
        let running = Arc::clone(&self.running);

        let handle = tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(30)); // 每30秒监控一次

            loop {
                interval.tick().await;

                // 检查是否还在运行
                if !*running.read().await {
                    break;
                }

                // 更新传输统计信息
                let transports = active_transports.read().await;
                for (transport_id, transport) in transports.iter() {
                    let stats = transport.get_stats().await;

                    // 更新存储中的统计信息
                    if let Ok(Some(mut transport_info)) = storage_manager.get_transport(transport_id).await {
                        transport_info.stats = stats;
                        let _ = storage_manager.store_transport(transport_id.clone(), transport_info).await;
                    }
                }

                debug!("传输监控任务执行完成，活跃传输数: {}", transports.len());
            }

            info!("传输监控任务结束");
        });

        let mut monitor_handle = self.monitor_task_handle.write().await;
        *monitor_handle = Some(handle);
    }

    /// 停止监控任务
    async fn stop_monitor_task(&self) {
        let mut monitor_handle = self.monitor_task_handle.write().await;
        if let Some(handle) = monitor_handle.take() {
            handle.abort();
            info!("传输监控任务已停止");
        }
    }

    /// 恢复之前的传输
    async fn restore_transports(&self) -> TransportResult<()> {
        let stored_transports = self.storage_manager.list_transports().await
            .map_err(|e| TransportError::CreationFailed {
                transport_type: "restore".to_string(),
                reason: e.to_string(),
            })?;

        for transport_info in stored_transports {
            // 只恢复活跃状态的传输
            if transport_info.status == TransportStatus::Active {
                match self.transport_factory.create_transport(
                    transport_info.transport_id.clone(),
                    transport_info.transport_type.clone(),
                    transport_info.config.clone(),
                ).await {
                    Ok(transport) => {
                        let mut active_transports = self.active_transports.write().await;
                        active_transports.insert(transport_info.transport_id.clone(), transport);
                        info!("恢复传输: {}", transport_info.transport_id);
                    }
                    Err(e) => {
                        warn!("恢复传输失败: {} - {}", transport_info.transport_id, e);
                    }
                }
            }
        }

        Ok(())
    }

    /// 停止所有传输
    async fn stop_all_transports(&self) -> TransportResult<()> {
        let active_transports = self.active_transports.read().await;

        for (transport_id, transport) in active_transports.iter() {
            if let Err(e) = transport.stop().await {
                warn!("停止传输失败: {} - {}", transport_id, e);
            }
        }

        info!("所有传输已停止");
        Ok(())
    }
}

impl TransportFactory {
    /// 创建新的传输工厂
    pub fn new(mediasoup_config: MediaSoupConfig) -> Self {
        Self {
            mediasoup_config,
        }
    }

    /// 创建传输实例
    pub async fn create_transport(
        &self,
        transport_id: TransportId,
        transport_type: TransportType,
        config: TransportConfig,
    ) -> TransportResult<Arc<dyn Transport>> {
        match transport_type {
            TransportType::MediaSoup => {
                let transport = MediaSoupTransport::new(
                    transport_id,
                    config,
                    self.mediasoup_config.clone(),
                ).await?;
                Ok(Arc::new(transport))
            }
            TransportType::TcpTunnel => {
                let transport = TcpTunnelTransport::new(transport_id, config).await?;
                Ok(Arc::new(transport))
            }
            TransportType::UdpTunnel => {
                let transport = UdpTunnelTransport::new(transport_id, config).await?;
                Ok(Arc::new(transport))
            }
            TransportType::Multicast => {
                let transport = MulticastTransport::new(transport_id, config).await?;
                Ok(Arc::new(transport))
            }
            TransportType::PortForward => {
                let transport = PortForwardTransport::new(transport_id, config).await?;
                Ok(Arc::new(transport))
            }
            TransportType::WebRTC => {
                let transport = WebRTCTransport::new(transport_id, config).await?;
                Ok(Arc::new(transport))
            }
        }
    }
}

// 具体传输实现

/// MediaSoup传输实现
pub struct MediaSoupTransport {
    transport_id: TransportId,
    config: TransportConfig,
    mediasoup_config: MediaSoupConfig,
    status: Arc<RwLock<TransportStatus>>,
    stats: Arc<RwLock<TransportStats>>,
}

impl MediaSoupTransport {
    pub async fn new(
        transport_id: TransportId,
        config: TransportConfig,
        mediasoup_config: MediaSoupConfig,
    ) -> TransportResult<Self> {
        Ok(Self {
            transport_id,
            config,
            mediasoup_config,
            status: Arc::new(RwLock::new(TransportStatus::Initializing)),
            stats: Arc::new(RwLock::new(TransportStats::default())),
        })
    }
}

#[async_trait]
impl Transport for MediaSoupTransport {
    fn transport_id(&self) -> &TransportId {
        &self.transport_id
    }

    fn transport_type(&self) -> TransportType {
        TransportType::MediaSoup
    }

    async fn status(&self) -> TransportStatus {
        *self.status.read().await
    }

    async fn start(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("MediaSoup传输启动: {}", self.transport_id);
        Ok(())
    }

    async fn stop(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Closed;
        info!("MediaSoup传输停止: {}", self.transport_id);
        Ok(())
    }

    async fn pause(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Paused;
        info!("MediaSoup传输暂停: {}", self.transport_id);
        Ok(())
    }

    async fn resume(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("MediaSoup传输恢复: {}", self.transport_id);
        Ok(())
    }

    async fn get_stats(&self) -> TransportStats {
        self.stats.read().await.clone()
    }

    async fn update_config(&self, _config: TransportConfig) -> TransportResult<()> {
        // TODO: 实现配置更新
        Ok(())
    }
}

/// TCP隧道传输实现
pub struct TcpTunnelTransport {
    transport_id: TransportId,
    config: TransportConfig,
    status: Arc<RwLock<TransportStatus>>,
    stats: Arc<RwLock<TransportStats>>,
}

impl TcpTunnelTransport {
    pub async fn new(
        transport_id: TransportId,
        config: TransportConfig,
    ) -> TransportResult<Self> {
        Ok(Self {
            transport_id,
            config,
            status: Arc::new(RwLock::new(TransportStatus::Initializing)),
            stats: Arc::new(RwLock::new(TransportStats::default())),
        })
    }
}

#[async_trait]
impl Transport for TcpTunnelTransport {
    fn transport_id(&self) -> &TransportId {
        &self.transport_id
    }

    fn transport_type(&self) -> TransportType {
        TransportType::TcpTunnel
    }

    async fn status(&self) -> TransportStatus {
        *self.status.read().await
    }

    async fn start(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("TCP隧道传输启动: {}", self.transport_id);
        Ok(())
    }

    async fn stop(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Closed;
        info!("TCP隧道传输停止: {}", self.transport_id);
        Ok(())
    }

    async fn pause(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Paused;
        info!("TCP隧道传输暂停: {}", self.transport_id);
        Ok(())
    }

    async fn resume(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("TCP隧道传输恢复: {}", self.transport_id);
        Ok(())
    }

    async fn get_stats(&self) -> TransportStats {
        self.stats.read().await.clone()
    }

    async fn update_config(&self, _config: TransportConfig) -> TransportResult<()> {
        // TODO: 实现配置更新
        Ok(())
    }
}

/// UDP隧道传输实现
pub struct UdpTunnelTransport {
    transport_id: TransportId,
    config: TransportConfig,
    status: Arc<RwLock<TransportStatus>>,
    stats: Arc<RwLock<TransportStats>>,
}

impl UdpTunnelTransport {
    pub async fn new(
        transport_id: TransportId,
        config: TransportConfig,
    ) -> TransportResult<Self> {
        Ok(Self {
            transport_id,
            config,
            status: Arc::new(RwLock::new(TransportStatus::Initializing)),
            stats: Arc::new(RwLock::new(TransportStats::default())),
        })
    }
}

#[async_trait]
impl Transport for UdpTunnelTransport {
    fn transport_id(&self) -> &TransportId {
        &self.transport_id
    }

    fn transport_type(&self) -> TransportType {
        TransportType::UdpTunnel
    }

    async fn status(&self) -> TransportStatus {
        *self.status.read().await
    }

    async fn start(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("UDP隧道传输启动: {}", self.transport_id);
        Ok(())
    }

    async fn stop(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Closed;
        info!("UDP隧道传输停止: {}", self.transport_id);
        Ok(())
    }

    async fn pause(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Paused;
        info!("UDP隧道传输暂停: {}", self.transport_id);
        Ok(())
    }

    async fn resume(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("UDP隧道传输恢复: {}", self.transport_id);
        Ok(())
    }

    async fn get_stats(&self) -> TransportStats {
        self.stats.read().await.clone()
    }

    async fn update_config(&self, _config: TransportConfig) -> TransportResult<()> {
        // TODO: 实现配置更新
        Ok(())
    }
}

/// 组播传输实现
pub struct MulticastTransport {
    transport_id: TransportId,
    config: TransportConfig,
    status: Arc<RwLock<TransportStatus>>,
    stats: Arc<RwLock<TransportStats>>,
}

impl MulticastTransport {
    pub async fn new(
        transport_id: TransportId,
        config: TransportConfig,
    ) -> TransportResult<Self> {
        Ok(Self {
            transport_id,
            config,
            status: Arc::new(RwLock::new(TransportStatus::Initializing)),
            stats: Arc::new(RwLock::new(TransportStats::default())),
        })
    }
}

#[async_trait]
impl Transport for MulticastTransport {
    fn transport_id(&self) -> &TransportId {
        &self.transport_id
    }

    fn transport_type(&self) -> TransportType {
        TransportType::Multicast
    }

    async fn status(&self) -> TransportStatus {
        *self.status.read().await
    }

    async fn start(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("组播传输启动: {}", self.transport_id);
        Ok(())
    }

    async fn stop(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Closed;
        info!("组播传输停止: {}", self.transport_id);
        Ok(())
    }

    async fn pause(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Paused;
        info!("组播传输暂停: {}", self.transport_id);
        Ok(())
    }

    async fn resume(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("组播传输恢复: {}", self.transport_id);
        Ok(())
    }

    async fn get_stats(&self) -> TransportStats {
        self.stats.read().await.clone()
    }

    async fn update_config(&self, _config: TransportConfig) -> TransportResult<()> {
        // TODO: 实现配置更新
        Ok(())
    }
}

/// 端口转发传输实现
pub struct PortForwardTransport {
    transport_id: TransportId,
    config: TransportConfig,
    status: Arc<RwLock<TransportStatus>>,
    stats: Arc<RwLock<TransportStats>>,
}

impl PortForwardTransport {
    pub async fn new(
        transport_id: TransportId,
        config: TransportConfig,
    ) -> TransportResult<Self> {
        Ok(Self {
            transport_id,
            config,
            status: Arc::new(RwLock::new(TransportStatus::Initializing)),
            stats: Arc::new(RwLock::new(TransportStats::default())),
        })
    }
}

#[async_trait]
impl Transport for PortForwardTransport {
    fn transport_id(&self) -> &TransportId {
        &self.transport_id
    }

    fn transport_type(&self) -> TransportType {
        TransportType::PortForward
    }

    async fn status(&self) -> TransportStatus {
        *self.status.read().await
    }

    async fn start(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("端口转发传输启动: {}", self.transport_id);
        Ok(())
    }

    async fn stop(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Closed;
        info!("端口转发传输停止: {}", self.transport_id);
        Ok(())
    }

    async fn pause(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Paused;
        info!("端口转发传输暂停: {}", self.transport_id);
        Ok(())
    }

    async fn resume(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("端口转发传输恢复: {}", self.transport_id);
        Ok(())
    }

    async fn get_stats(&self) -> TransportStats {
        self.stats.read().await.clone()
    }

    async fn update_config(&self, _config: TransportConfig) -> TransportResult<()> {
        // TODO: 实现配置更新
        Ok(())
    }
}

/// WebRTC传输实现
pub struct WebRTCTransport {
    transport_id: TransportId,
    config: TransportConfig,
    status: Arc<RwLock<TransportStatus>>,
    stats: Arc<RwLock<TransportStats>>,
}

impl WebRTCTransport {
    pub async fn new(
        transport_id: TransportId,
        config: TransportConfig,
    ) -> TransportResult<Self> {
        Ok(Self {
            transport_id,
            config,
            status: Arc::new(RwLock::new(TransportStatus::Initializing)),
            stats: Arc::new(RwLock::new(TransportStats::default())),
        })
    }
}

#[async_trait]
impl Transport for WebRTCTransport {
    fn transport_id(&self) -> &TransportId {
        &self.transport_id
    }

    fn transport_type(&self) -> TransportType {
        TransportType::WebRTC
    }

    async fn status(&self) -> TransportStatus {
        *self.status.read().await
    }

    async fn start(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("WebRTC传输启动: {}", self.transport_id);
        Ok(())
    }

    async fn stop(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Closed;
        info!("WebRTC传输停止: {}", self.transport_id);
        Ok(())
    }

    async fn pause(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Paused;
        info!("WebRTC传输暂停: {}", self.transport_id);
        Ok(())
    }

    async fn resume(&self) -> TransportResult<()> {
        let mut status = self.status.write().await;
        *status = TransportStatus::Active;
        info!("WebRTC传输恢复: {}", self.transport_id);
        Ok(())
    }

    async fn get_stats(&self) -> TransportStats {
        self.stats.read().await.clone()
    }

    async fn update_config(&self, _config: TransportConfig) -> TransportResult<()> {
        // TODO: 实现配置更新
        Ok(())
    }
}
