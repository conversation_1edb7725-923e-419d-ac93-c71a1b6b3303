// 传输管理器占位符
// TODO: 完整实现传输管理功能

use std::sync::Arc;
use crate::config::MediaSoupConfig;
use crate::error::{TransportError, TransportResult};
use crate::storage::StorageManager;

pub struct TransportManager {
    config: MediaSoupConfig,
    storage_manager: Arc<StorageManager>,
}

impl TransportManager {
    pub async fn new(
        config: MediaSoupConfig,
        storage_manager: Arc<StorageManager>,
    ) -> TransportResult<Self> {
        Ok(Self {
            config,
            storage_manager,
        })
    }

    pub async fn start(&self) -> TransportResult<()> {
        tracing::info!("传输管理器启动");
        Ok(())
    }

    pub async fn stop(&self) -> TransportResult<()> {
        tracing::info!("传输管理器停止");
        Ok(())
    }
}
