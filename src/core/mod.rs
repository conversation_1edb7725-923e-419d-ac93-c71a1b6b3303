// 核心模块
// 包含转发节点的核心组件

pub mod node_manager;
pub mod session_manager;
pub mod transport_manager;
pub mod connection_manager;
pub mod forwarder_node;

pub use forwarder_node::ForwarderNode;

use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, error};

use crate::config::ForwarderConfig;
use crate::error::{ForwarderError, Result};
use crate::storage::{StorageManager, SessionStore, TransportStore, ConnectionStore, StatisticsStore};
use crate::security::SecurityManager;
use crate::network::NetworkManager;
use crate::monitoring::MonitoringManager;

/// 核心管理器
/// 协调各个子系统的运行
pub struct CoreManager {
    /// 配置
    config: Arc<RwLock<ForwarderConfig>>,
    /// 存储管理器
    storage_manager: Arc<StorageManager>,
    /// 安全管理器
    security_manager: Arc<SecurityManager>,
    /// 网络管理器
    network_manager: Arc<RwLock<NetworkManager>>,
    /// 监控管理器
    monitoring_manager: Arc<MonitoringManager>,
    /// 节点管理器
    node_manager: Arc<node_manager::NodeManager>,
    /// 会话管理器
    session_manager: Arc<session_manager::SessionManager>,
    /// 传输管理器
    transport_manager: Arc<transport_manager::TransportManager>,
    /// 连接管理器
    connection_manager: Arc<connection_manager::ConnectionManager>,
}

impl CoreManager {
    /// 创建新的核心管理器
    pub async fn new(config: ForwarderConfig) -> Result<Self> {
        info!("初始化核心管理器");

        // 创建存储管理器
        let storage_manager = Arc::new(
            StorageManager::new(config.storage.clone()).await?
        );

        // 创建安全管理器
        let security_manager = Arc::new(
            SecurityManager::new(config.security.clone()).await?
        );

        // 创建网络管理器
        let network_manager = Arc::new(RwLock::new(
            NetworkManager::new(config.network.clone())
        ));

        // 创建监控管理器
        let monitoring_manager = Arc::new(
            MonitoringManager::new(config.monitoring.clone()).await?
        );

        // 创建节点管理器
        let node_manager = Arc::new(
            node_manager::NodeManager::new(
                config.node.clone(),
                storage_manager.clone(),
                security_manager.clone(),
            ).await?
        );

        // 创建会话管理器
        let session_manager = Arc::new(
            session_manager::SessionManager::new(
                storage_manager.clone(),
                security_manager.clone(),
            ).await?
        );

        // 创建传输管理器
        let transport_manager = Arc::new(
            transport_manager::TransportManager::new(
                config.mediasoup.clone(),
                storage_manager.clone(),
            ).await?
        );

        // 创建连接管理器
        let connection_manager = Arc::new(
            connection_manager::ConnectionManager::new(
                config.network.clone(),
                storage_manager.clone(),
            ).await?
        );

        let config = Arc::new(RwLock::new(config));

        Ok(Self {
            config,
            storage_manager,
            security_manager,
            network_manager,
            monitoring_manager,
            node_manager,
            session_manager,
            transport_manager,
            connection_manager,
        })
    }

    /// 启动核心管理器
    pub async fn start(&self) -> Result<()> {
        info!("启动核心管理器");

        // 启动存储管理器
        self.storage_manager.start().await?;

        // 启动网络管理器
        self.network_manager.write().await.start().await?;

        // 启动监控管理器
        self.monitoring_manager.start().await?;

        // 启动节点管理器
        self.node_manager.start().await?;

        // 启动会话管理器
        self.session_manager.start().await?;

        // 启动传输管理器
        self.transport_manager.start().await?;

        // 启动连接管理器
        self.connection_manager.start().await?;

        info!("核心管理器启动完成");
        Ok(())
    }

    /// 停止核心管理器
    pub async fn stop(&self) -> Result<()> {
        info!("停止核心管理器");

        // 按相反顺序停止各个管理器
        if let Err(e) = self.connection_manager.stop().await {
            error!("停止连接管理器失败: {}", e);
        }

        if let Err(e) = self.transport_manager.stop().await {
            error!("停止传输管理器失败: {}", e);
        }

        if let Err(e) = self.session_manager.stop().await {
            error!("停止会话管理器失败: {}", e);
        }

        if let Err(e) = self.node_manager.stop().await {
            error!("停止节点管理器失败: {}", e);
        }

        if let Err(e) = self.monitoring_manager.stop().await {
            error!("停止监控管理器失败: {}", e);
        }

        if let Err(e) = self.network_manager.write().await.stop().await {
            error!("停止网络管理器失败: {}", e);
        }

        if let Err(e) = self.storage_manager.stop().await {
            error!("停止存储管理器失败: {}", e);
        }

        info!("核心管理器停止完成");
        Ok(())
    }

    /// 获取配置
    pub async fn get_config(&self) -> ForwarderConfig {
        self.config.read().await.clone()
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: ForwarderConfig) -> Result<()> {
        info!("更新核心配置");

        // 更新配置
        {
            let mut config = self.config.write().await;
            *config = new_config.clone();
        }

        // 通知各个管理器更新配置
        self.node_manager.update_config(new_config.node.clone()).await?;
        self.network_manager.write().await.update_config(new_config.network.clone()).await?;
        self.monitoring_manager.update_config(new_config.monitoring.clone()).await?;

        info!("核心配置更新完成");
        Ok(())
    }

    /// 获取存储管理器
    pub fn storage_manager(&self) -> Arc<StorageManager> {
        self.storage_manager.clone()
    }

    /// 获取安全管理器
    pub fn security_manager(&self) -> Arc<SecurityManager> {
        self.security_manager.clone()
    }

    /// 获取网络管理器
    pub fn network_manager(&self) -> Arc<RwLock<NetworkManager>> {
        self.network_manager.clone()
    }

    /// 获取监控管理器
    pub fn monitoring_manager(&self) -> Arc<MonitoringManager> {
        self.monitoring_manager.clone()
    }

    /// 获取节点管理器
    pub fn node_manager(&self) -> Arc<node_manager::NodeManager> {
        self.node_manager.clone()
    }

    /// 获取会话管理器
    pub fn session_manager(&self) -> Arc<session_manager::SessionManager> {
        self.session_manager.clone()
    }

    /// 获取传输管理器
    pub fn transport_manager(&self) -> Arc<transport_manager::TransportManager> {
        self.transport_manager.clone()
    }

    /// 获取连接管理器
    pub fn connection_manager(&self) -> Arc<connection_manager::ConnectionManager> {
        self.connection_manager.clone()
    }

    /// 获取系统健康状态
    pub async fn get_health_status(&self) -> HealthStatus {
        let storage_health = self.storage_manager.get_health().await.unwrap_or_default();
        let network_status = self.network_manager.read().await.get_network_status().await;
        let node_status = self.node_manager.get_status().await;

        HealthStatus {
            overall_healthy: storage_health.memory_healthy 
                && storage_health.persistent_healthy
                && network_status.api_server_running
                && matches!(node_status, crate::types::NodeStatus::Online),
            storage_health,
            network_status,
            node_status,
            last_check: chrono::Utc::now(),
        }
    }

    /// 获取系统统计信息
    pub async fn get_system_stats(&self) -> SystemStats {
        let storage_stats = self.storage_manager.get_health().await.unwrap_or_default();
        let network_stats = self.network_manager.read().await.get_network_stats().await;
        let session_count = self.storage_manager.memory().session_count().await;
        let transport_count = self.storage_manager.memory().transport_count().await;
        let connection_count = self.storage_manager.memory().connection_count().await;

        SystemStats {
            session_count,
            transport_count,
            connection_count,
            network_stats,
            memory_usage_percent: storage_stats.memory_usage_percent,
            disk_usage_percent: storage_stats.disk_usage_percent,
            uptime_seconds: self.node_manager.get_uptime().await,
        }
    }
}

/// 健康状态
#[derive(Debug, Clone)]
pub struct HealthStatus {
    /// 整体健康状态
    pub overall_healthy: bool,
    /// 存储健康状态
    pub storage_health: crate::storage::StorageHealth,
    /// 网络状态
    pub network_status: crate::network::NetworkStatus,
    /// 节点状态
    pub node_status: crate::types::NodeStatus,
    /// 最后检查时间
    pub last_check: chrono::DateTime<chrono::Utc>,
}

/// 系统统计信息
#[derive(Debug, Clone)]
pub struct SystemStats {
    /// 会话数量
    pub session_count: usize,
    /// 传输数量
    pub transport_count: usize,
    /// 连接数量
    pub connection_count: usize,
    /// 网络统计
    pub network_stats: crate::network::NetworkStats,
    /// 内存使用率
    pub memory_usage_percent: f32,
    /// 磁盘使用率
    pub disk_usage_percent: f32,
    /// 运行时间(秒)
    pub uptime_seconds: u64,
}
