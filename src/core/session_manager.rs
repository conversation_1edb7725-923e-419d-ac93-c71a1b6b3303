// 会话管理器实现
// 负责管理所有活跃的转发会话，处理会话的创建、更新、销毁

use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::{RwLock, Mutex};
use tokio::time::{Duration, interval};
use tracing::{info, warn, error, debug};
use chrono::Utc;

use crate::error::{SessionError, SessionResult};
use crate::storage::{StorageManager, SessionStore};
use crate::security::SecurityManager;
use crate::types::{
    SessionId, SessionInfo, SessionType, SessionStatus, SessionConfig,
    SessionStats, SessionSummary, SessionMetrics, TransmitterConfig,
    ReceiverConfig, generate_session_id
};

/// 会话管理器
pub struct SessionManager {
    /// 存储管理器
    storage_manager: Arc<StorageManager>,
    /// 安全管理器
    security_manager: Arc<SecurityManager>,
    /// 活跃会话缓存
    active_sessions: Arc<RwLock<HashMap<SessionId, Arc<SessionInfo>>>>,
    /// 会话状态锁
    session_locks: Arc<Mutex<HashMap<SessionId, Arc<Mutex<()>>>>>,
    /// 会话统计
    session_metrics: Arc<RwLock<SessionMetrics>>,
    /// 会话清理任务句柄
    cleanup_handle: Arc<Mutex<Option<tokio::task::JoinHandle<()>>>>,
    /// 是否正在运行
    is_running: Arc<RwLock<bool>>,
}

impl SessionManager {
    /// 创建新的会话管理器
    pub async fn new(
        storage_manager: Arc<StorageManager>,
        security_manager: Arc<SecurityManager>,
    ) -> SessionResult<Self> {
        let manager = Self {
            storage_manager,
            security_manager,
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
            session_locks: Arc::new(Mutex::new(HashMap::new())),
            session_metrics: Arc::new(RwLock::new(SessionMetrics::default())),
            cleanup_handle: Arc::new(Mutex::new(None)),
            is_running: Arc::new(RwLock::new(false)),
        };

        // 从存储中恢复会话
        manager.restore_sessions_from_storage().await?;

        Ok(manager)
    }

    /// 启动会话管理器
    pub async fn start(&self) -> SessionResult<()> {
        let mut is_running = self.is_running.write().await;
        if *is_running {
            return Err(SessionError::ManagerAlreadyRunning);
        }

        info!("启动会话管理器");

        // 启动会话清理任务
        self.start_cleanup_task().await;

        *is_running = true;
        info!("会话管理器启动完成");
        Ok(())
    }

    /// 停止会话管理器
    pub async fn stop(&self) -> SessionResult<()> {
        let mut is_running = self.is_running.write().await;
        if !*is_running {
            return Ok(());
        }

        info!("停止会话管理器");

        // 停止清理任务
        if let Some(handle) = self.cleanup_handle.lock().await.take() {
            handle.abort();
        }

        // 保存所有活跃会话到存储
        self.save_all_sessions().await?;

        *is_running = false;
        info!("会话管理器停止完成");
        Ok(())
    }

    /// 创建发送端会话
    pub async fn create_transmitter_session(
        &self,
        config: TransmitterConfig,
    ) -> SessionResult<SessionId> {
        debug!("创建发送端会话: device_id={}", config.device_id);

        // 验证配置
        self.validate_transmitter_config(&config).await?;

        // 创建会话配置
        let session_config = SessionConfig {
            session_type: SessionType::Transmitter,
            device_id: config.device_id.clone(),
            transports: config.transports,
            security: config.security,
            qos: config.qos,
        };

        // 创建会话
        self.create_session_internal(session_config).await
    }

    /// 创建接收端会话
    pub async fn create_receiver_session(
        &self,
        config: ReceiverConfig,
    ) -> SessionResult<SessionId> {
        debug!("创建接收端会话: device_id={}", config.device_id);

        // 验证配置
        self.validate_receiver_config(&config).await?;

        // 创建会话配置
        let session_config = SessionConfig {
            session_type: SessionType::Receiver,
            device_id: config.device_id.clone(),
            transports: config.transports,
            security: config.security,
            qos: config.qos,
        };

        // 创建会话
        self.create_session_internal(session_config).await
    }

    /// 销毁会话
    pub async fn destroy_session(&self, session_id: SessionId) -> SessionResult<()> {
        info!("销毁会话: session_id={}", session_id);

        // 获取会话锁
        let _lock = self.get_session_lock(&session_id).await;

        // 检查会话是否存在
        let session = {
            let sessions = self.active_sessions.read().await;
            sessions.get(&session_id).cloned()
        };

        let session = session.ok_or_else(|| SessionError::SessionNotFound {
            session_id: session_id.clone(),
        })?;

        // 更新会话状态为终止中
        self.update_session_status(&session_id, SessionStatus::Terminating).await?;

        // 清理会话资源
        self.cleanup_session_resources(&session_id).await?;

        // 从活跃会话中移除
        {
            let mut sessions = self.active_sessions.write().await;
            sessions.remove(&session_id);
        }

        // 从存储中删除
        self.storage_manager.remove_session(&session_id).await
            .map_err(|e| SessionError::StorageError { source: e })?;

        // 更新统计
        self.update_session_metrics_on_destroy().await;

        info!("会话销毁完成: session_id={}", session_id);
        Ok(())
    }

    /// 获取会话信息
    pub async fn get_session_info(&self, session_id: SessionId) -> SessionResult<SessionInfo> {
        // 首先从缓存中查找
        {
            let sessions = self.active_sessions.read().await;
            if let Some(session) = sessions.get(&session_id) {
                return Ok((**session).clone());
            }
        }

        // 从存储中查找
        let session = self.storage_manager.get_session(&session_id).await
            .map_err(|e| SessionError::StorageError { source: e })?
            .ok_or_else(|| SessionError::SessionNotFound {
                session_id: session_id.clone(),
            })?;

        Ok(session)
    }

    /// 列出所有会话
    pub fn list_sessions(&self) -> Vec<SessionSummary> {
        // 这里使用阻塞方式获取，因为接口要求同步返回
        let sessions = match self.active_sessions.try_read() {
            Ok(sessions) => sessions,
            Err(_) => {
                warn!("无法获取会话列表锁，返回空列表");
                return Vec::new();
            }
        };

        sessions.values()
            .map(|session| SessionSummary::from(session.as_ref()))
            .collect()
    }

    /// 更新会话状态
    pub async fn update_session_status(
        &self,
        session_id: &SessionId,
        status: SessionStatus,
    ) -> SessionResult<()> {
        debug!("更新会话状态: session_id={}, status={:?}", session_id, status);

        // 获取会话锁
        let _lock = self.get_session_lock(session_id).await;

        // 更新缓存中的会话状态
        {
            let mut sessions = self.active_sessions.write().await;
            if let Some(session) = sessions.get_mut(session_id) {
                let mut session_info = (**session).clone();
                session_info.status = status.clone();
                session_info.updated_at = Utc::now();
                *session = Arc::new(session_info);
            }
        }

        // 更新存储中的会话状态
        self.storage_manager.update_session_status(session_id, status).await
            .map_err(|e| SessionError::StorageError { source: e })?;

        Ok(())
    }

    /// 检查会话是否存在
    pub async fn session_exists(&self, session_id: &SessionId) -> bool {
        // 首先检查缓存
        {
            let sessions = self.active_sessions.read().await;
            if sessions.contains_key(session_id) {
                return true;
            }
        }

        // 检查存储
        self.storage_manager.session_exists(session_id).await
    }

    /// 获取会话数量
    pub async fn session_count(&self) -> usize {
        let sessions = self.active_sessions.read().await;
        sessions.len()
    }

    /// 获取会话统计
    pub async fn get_session_metrics(&self) -> SessionMetrics {
        let metrics = self.session_metrics.read().await;
        metrics.clone()
    }

    /// 更新会话统计信息
    pub async fn update_session_stats(
        &self,
        session_id: &SessionId,
        stats: SessionStats,
    ) -> SessionResult<()> {
        debug!("更新会话统计: session_id={}", session_id);

        // 获取会话锁
        let _lock = self.get_session_lock(session_id).await;

        // 更新缓存中的会话统计
        {
            let mut sessions = self.active_sessions.write().await;
            if let Some(session) = sessions.get_mut(session_id) {
                let mut session_info = (**session).clone();
                session_info.stats = stats;
                session_info.updated_at = Utc::now();
                *session = Arc::new(session_info);
            }
        }

        // 保存到存储
        if let Some(session) = self.active_sessions.read().await.get(session_id) {
            self.storage_manager.store_session(session_id.clone(), (**session).clone()).await
                .map_err(|e| SessionError::StorageError { source: e })?;
        }

        Ok(())
    }

    /// 获取活跃会话列表
    pub async fn get_active_sessions(&self) -> Vec<SessionInfo> {
        let sessions = self.active_sessions.read().await;
        sessions.values()
            .filter(|session| matches!(session.status, SessionStatus::Active))
            .map(|session| (**session).clone())
            .collect()
    }

    /// 获取指定类型的会话
    pub async fn get_sessions_by_type(&self, session_type: SessionType) -> Vec<SessionInfo> {
        let sessions = self.active_sessions.read().await;
        sessions.values()
            .filter(|session| session.session_type == session_type)
            .map(|session| (**session).clone())
            .collect()
    }

    /// 获取指定设备的会话
    pub async fn get_sessions_by_device(&self, device_id: &str) -> Vec<SessionInfo> {
        let sessions = self.active_sessions.read().await;
        sessions.values()
            .filter(|session| session.device_id == device_id)
            .map(|session| (**session).clone())
            .collect()
    }

    // ========== 内部辅助方法 ==========

    /// 内部会话创建方法
    async fn create_session_internal(&self, config: SessionConfig) -> SessionResult<SessionId> {
        // 生成会话ID
        let session_id = generate_session_id(&config.session_type);

        debug!("创建会话: session_id={}, type={:?}", session_id, config.session_type);

        // 获取会话锁
        let _lock = self.get_session_lock(&session_id).await;

        // 检查设备是否已有活跃会话
        self.check_device_session_limit(&config.device_id).await?;

        // 创建会话信息
        let now = Utc::now();
        let session_info = SessionInfo {
            session_id: session_id.clone(),
            session_type: config.session_type.clone(),
            status: SessionStatus::Initializing,
            device_id: config.device_id.clone(),
            created_at: now,
            updated_at: now,
            config,
            stats: SessionStats::default(),
        };

        // 存储会话
        self.storage_manager.store_session(session_id.clone(), session_info.clone()).await
            .map_err(|e| SessionError::StorageError { source: e })?;

        // 添加到活跃会话缓存
        {
            let mut sessions = self.active_sessions.write().await;
            sessions.insert(session_id.clone(), Arc::new(session_info));
        }

        // 更新统计
        self.update_session_metrics_on_create().await;

        // 异步初始化会话
        self.initialize_session(&session_id).await?;

        info!("会话创建完成: session_id={}", session_id);
        Ok(session_id)
    }

    /// 验证发送端配置
    async fn validate_transmitter_config(&self, config: &TransmitterConfig) -> SessionResult<()> {
        if config.device_id.is_empty() {
            return Err(SessionError::InvalidConfig {
                reason: "设备ID不能为空".to_string(),
            });
        }

        if config.device_id.len() > 64 {
            return Err(SessionError::InvalidConfig {
                reason: "设备ID长度不能超过64个字符".to_string(),
            });
        }

        if config.transports.is_empty() {
            return Err(SessionError::InvalidConfig {
                reason: "至少需要配置一个传输".to_string(),
            });
        }

        // 验证传输配置
        for transport in &config.transports {
            self.validate_transport_config(transport).await?;
        }

        Ok(())
    }

    /// 验证接收端配置
    async fn validate_receiver_config(&self, config: &ReceiverConfig) -> SessionResult<()> {
        if config.device_id.is_empty() {
            return Err(SessionError::InvalidConfig {
                reason: "设备ID不能为空".to_string(),
            });
        }

        if config.device_id.len() > 64 {
            return Err(SessionError::InvalidConfig {
                reason: "设备ID长度不能超过64个字符".to_string(),
            });
        }

        if config.transports.is_empty() {
            return Err(SessionError::InvalidConfig {
                reason: "至少需要配置一个传输".to_string(),
            });
        }

        // 验证传输配置
        for transport in &config.transports {
            self.validate_transport_config(transport).await?;
        }

        Ok(())
    }

    /// 验证传输配置
    async fn validate_transport_config(&self, _config: &crate::types::TransportConfig) -> SessionResult<()> {
        // TODO: 实现传输配置验证逻辑
        // 这里可以验证端口范围、编解码器支持等
        Ok(())
    }

    /// 检查设备会话限制
    async fn check_device_session_limit(&self, device_id: &str) -> SessionResult<()> {
        let device_sessions = self.get_sessions_by_device(device_id).await;
        let active_count = device_sessions.iter()
            .filter(|session| matches!(session.status, SessionStatus::Active | SessionStatus::Connecting))
            .count();

        // 每个设备最多允许2个活跃会话（一个发送端，一个接收端）
        if active_count >= 2 {
            return Err(SessionError::DeviceSessionLimitExceeded {
                device_id: device_id.to_string(),
                limit: 2,
                current: active_count,
            });
        }

        Ok(())
    }

    /// 初始化会话
    async fn initialize_session(&self, session_id: &SessionId) -> SessionResult<()> {
        debug!("初始化会话: session_id={}", session_id);

        // 更新状态为配置中
        self.update_session_status(session_id, SessionStatus::Configuring).await?;

        // TODO: 这里可以添加具体的会话初始化逻辑
        // 例如：创建传输、建立连接等

        // 模拟初始化过程
        tokio::time::sleep(Duration::from_millis(100)).await;

        // 更新状态为连接中
        self.update_session_status(session_id, SessionStatus::Connecting).await?;

        // 模拟连接过程
        tokio::time::sleep(Duration::from_millis(100)).await;

        // 更新状态为活跃
        self.update_session_status(session_id, SessionStatus::Active).await?;

        debug!("会话初始化完成: session_id={}", session_id);
        Ok(())
    }

    /// 清理会话资源
    async fn cleanup_session_resources(&self, session_id: &SessionId) -> SessionResult<()> {
        debug!("清理会话资源: session_id={}", session_id);

        // TODO: 这里可以添加具体的资源清理逻辑
        // 例如：关闭传输、释放端口、清理连接等

        Ok(())
    }

    /// 获取会话锁
    async fn get_session_lock(&self, session_id: &SessionId) -> Arc<Mutex<()>> {
        let mut locks = self.session_locks.lock().await;
        locks.entry(session_id.clone())
            .or_insert_with(|| Arc::new(Mutex::new(())))
            .clone()
    }

    /// 从存储中恢复会话
    async fn restore_sessions_from_storage(&self) -> SessionResult<()> {
        info!("从存储中恢复会话");

        let sessions = self.storage_manager.list_sessions().await
            .map_err(|e| SessionError::StorageError { source: e })?;

        let mut active_sessions = self.active_sessions.write().await;
        for session in sessions {
            // 只恢复活跃状态的会话
            if matches!(session.status, SessionStatus::Active | SessionStatus::Connecting) {
                active_sessions.insert(session.session_id.clone(), Arc::new(session));
            }
        }

        info!("恢复了 {} 个活跃会话", active_sessions.len());
        Ok(())
    }

    /// 保存所有会话到存储
    async fn save_all_sessions(&self) -> SessionResult<()> {
        info!("保存所有会话到存储");

        let sessions = self.active_sessions.read().await;
        for (session_id, session) in sessions.iter() {
            if let Err(e) = self.storage_manager.store_session(session_id.clone(), (**session).clone()).await {
                error!("保存会话失败: session_id={}, error={}", session_id, e);
            }
        }

        Ok(())
    }

    /// 启动会话清理任务
    async fn start_cleanup_task(&self) {
        let active_sessions = self.active_sessions.clone();
        let storage_manager = self.storage_manager.clone();
        let is_running = self.is_running.clone();

        let handle = tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(300)); // 每5分钟清理一次

            loop {
                interval.tick().await;

                // 检查是否还在运行
                if !*is_running.read().await {
                    break;
                }

                // 清理过期会话
                Self::cleanup_expired_sessions(&active_sessions, &storage_manager).await;
            }
        });

        *self.cleanup_handle.lock().await = Some(handle);
    }

    /// 清理过期会话
    async fn cleanup_expired_sessions(
        active_sessions: &Arc<RwLock<HashMap<SessionId, Arc<SessionInfo>>>>,
        storage_manager: &Arc<StorageManager>,
    ) {
        let now = Utc::now();
        let mut expired_sessions = Vec::new();

        // 查找过期会话
        {
            let sessions = active_sessions.read().await;
            for (session_id, session) in sessions.iter() {
                // 如果会话超过24小时没有更新，认为已过期
                if now.signed_duration_since(session.updated_at).num_hours() > 24 {
                    expired_sessions.push(session_id.clone());
                }
            }
        }

        // 清理过期会话
        if !expired_sessions.is_empty() {
            warn!("发现 {} 个过期会话，开始清理", expired_sessions.len());

            let mut sessions = active_sessions.write().await;
            for session_id in expired_sessions {
                sessions.remove(&session_id);

                // 从存储中删除
                if let Err(e) = storage_manager.remove_session(&session_id).await {
                    error!("删除过期会话失败: session_id={}, error={}", session_id, e);
                }
            }
        }
    }

    /// 更新会话创建统计
    async fn update_session_metrics_on_create(&self) {
        let mut metrics = self.session_metrics.write().await;
        metrics.total_sessions += 1;
        metrics.active_sessions += 1;
    }

    /// 更新会话销毁统计
    async fn update_session_metrics_on_destroy(&self) {
        let mut metrics = self.session_metrics.write().await;
        if metrics.active_sessions > 0 {
            metrics.active_sessions -= 1;
        }
    }

    /// 更新会话失败统计
    async fn update_session_metrics_on_failure(&self) {
        let mut metrics = self.session_metrics.write().await;
        metrics.failed_sessions += 1;
        if metrics.active_sessions > 0 {
            metrics.active_sessions -= 1;
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{StorageConfig, MemoryStorageConfig, DiskStorageConfig};
    use crate::types::{TransportConfig, TransportType, QosConfig, AddressConfig, PortRange, Protocol};
    use std::sync::Arc;
    use tokio::test;

    async fn create_mock_security_manager() -> SecurityManager {
        // 创建一个不需要实际证书文件的SecurityManager
        // 这里我们直接创建一个空的SecurityManager用于测试
        use crate::security::certificate::CertificateManager;
        use crate::security::access_control::AccessControlManager;
        use crate::security::audit::SecurityAuditor;
        use crate::security::rate_limit::RateLimiter;
        use crate::config::{SecurityConfig, ApiPermissionConfig, RateLimitConfig};

        let config = SecurityConfig {
            node_certificate_path: "".to_string(),
            node_private_key_path: "".to_string(),
            ca_certificate_path: "".to_string(),
            certificate_rotation_days: 90,
            certificate_check_interval_hours: 1,
            trusted_servers: vec![],
            api_permissions: ApiPermissionConfig {
                management_only_apis: vec![],
                inter_node_only_apis: vec![],
                both_callable_apis: vec![],
            },
            rate_limits: RateLimitConfig {
                enabled: false,
                max_requests_per_minute: 100,
                burst_size: 10,
                window_size_seconds: 60,
            },
        };

        // 创建各个组件（这些在测试中不会真正使用）
        let cert_manager = CertificateManager::new(config.clone());
        let access_control = AccessControlManager::new(config.api_permissions.clone());
        let auditor = SecurityAuditor::new();
        let rate_limiter = RateLimiter::new(config.rate_limits.clone());

        SecurityManager {
            config,
            cert_manager,
            access_control,
            auditor,
            rate_limiter,
        }
    }

    async fn create_test_session_manager() -> SessionResult<SessionManager> {
        let storage_config = StorageConfig {
            memory: MemoryStorageConfig {
                max_sessions: 100,
                max_transports: 500,
                cleanup_interval_seconds: 60,
            },
            disk: DiskStorageConfig {
                base_path: "/tmp/kvm_tunnel_test".to_string(),
                max_log_size_mb: 10,
                log_retention_days: 7,
                sync_interval_seconds: 60,
            },
        };

        let storage_manager = Arc::new(StorageManager::new(storage_config).await.unwrap());

        // 创建一个简单的模拟SecurityManager，不需要实际的证书文件
        let security_manager = Arc::new(create_mock_security_manager().await);

        SessionManager::new(storage_manager, security_manager).await
    }

    fn create_test_transmitter_config() -> TransmitterConfig {
        TransmitterConfig {
            device_id: "test-device-001".to_string(),
            transports: vec![
                TransportConfig {
                    transport_type: TransportType::MediaSoup,
                    addresses: vec![
                        AddressConfig {
                            ip: "127.0.0.1".to_string(),
                            port: PortRange::Single(8080),
                            protocol: Protocol::TCP,
                        },
                    ],
                    security: None,
                    qos: None,
                },
            ],
            security: None,
            qos: Some(QosConfig {
                priority: 1,
                max_bandwidth_mbps: Some(10),
                max_latency_ms: Some(100),
            }),
        }
    }

    fn create_test_receiver_config() -> ReceiverConfig {
        ReceiverConfig {
            device_id: "test-device-002".to_string(),
            transports: vec![
                TransportConfig {
                    transport_type: TransportType::MediaSoup,
                    addresses: vec![
                        AddressConfig {
                            ip: "127.0.0.1".to_string(),
                            port: PortRange::Single(8082),
                            protocol: Protocol::TCP,
                        },
                    ],
                    security: None,
                    qos: None,
                },
            ],
            security: None,
            qos: Some(QosConfig {
                priority: 1,
                max_bandwidth_mbps: Some(10),
                max_latency_ms: Some(100),
            }),
        }
    }

    #[test]
    async fn test_session_manager_creation() {
        let manager = create_test_session_manager().await.unwrap();
        assert_eq!(manager.session_count().await, 0);
    }

    #[test]
    async fn test_create_transmitter_session() {
        let manager = create_test_session_manager().await.unwrap();
        manager.start().await.unwrap();

        let config = create_test_transmitter_config();
        let session_id = manager.create_transmitter_session(config).await.unwrap();

        assert!(!session_id.is_empty());
        assert!(session_id.starts_with("sess_tx_"));
        assert!(manager.session_exists(&session_id).await);
        assert_eq!(manager.session_count().await, 1);

        manager.stop().await.unwrap();
    }

    #[test]
    async fn test_create_receiver_session() {
        let manager = create_test_session_manager().await.unwrap();
        manager.start().await.unwrap();

        let config = create_test_receiver_config();
        let session_id = manager.create_receiver_session(config).await.unwrap();

        assert!(!session_id.is_empty());
        assert!(session_id.starts_with("sess_rx_"));
        assert!(manager.session_exists(&session_id).await);
        assert_eq!(manager.session_count().await, 1);

        manager.stop().await.unwrap();
    }

    #[test]
    async fn test_get_session_info() {
        let manager = create_test_session_manager().await.unwrap();
        manager.start().await.unwrap();

        let config = create_test_transmitter_config();
        let session_id = manager.create_transmitter_session(config.clone()).await.unwrap();

        let session_info = manager.get_session_info(session_id.clone()).await.unwrap();
        assert_eq!(session_info.session_id, session_id);
        assert_eq!(session_info.session_type, SessionType::Transmitter);
        assert_eq!(session_info.device_id, config.device_id);
        assert_eq!(session_info.status, SessionStatus::Active);

        manager.stop().await.unwrap();
    }

    #[test]
    async fn test_destroy_session() {
        let manager = create_test_session_manager().await.unwrap();
        manager.start().await.unwrap();

        let config = create_test_transmitter_config();
        let session_id = manager.create_transmitter_session(config).await.unwrap();

        assert!(manager.session_exists(&session_id).await);
        assert_eq!(manager.session_count().await, 1);

        manager.destroy_session(session_id.clone()).await.unwrap();

        assert!(!manager.session_exists(&session_id).await);
        assert_eq!(manager.session_count().await, 0);

        manager.stop().await.unwrap();
    }
}
