// 节点管理器
// 负责节点的生命周期管理、与转发管理服务器通信、心跳维持

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{info, warn, error, debug};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::config::NodeConfig;
use crate::error::{NodeError, NodeResult, ForwarderError};
use crate::storage::StorageManager;
use crate::security::SecurityManager;
use crate::types::{NodeInfo, NodeStatus, NodeCapabilities, NodeType, NodeStatistics};

/// 节点管理器
pub struct NodeManager {
    /// 节点配置
    config: Arc<RwLock<NodeConfig>>,
    /// 节点信息
    node_info: Arc<RwLock<NodeInfo>>,
    /// 节点状态
    node_status: Arc<RwLock<NodeStatus>>,
    /// 存储管理器
    storage_manager: Arc<StorageManager>,
    /// 安全管理器
    security_manager: Arc<SecurityManager>,
    /// HTTP客户端
    http_client: reqwest::Client,
    /// 心跳任务句柄
    heartbeat_task: Arc<RwLock<Option<tokio::task::JoinHandle<()>>>>,
    /// 启动时间
    start_time: Instant,
}

impl NodeManager {
    /// 创建新的节点管理器
    pub async fn new(
        config: NodeConfig,
        storage_manager: Arc<StorageManager>,
        security_manager: Arc<SecurityManager>,
    ) -> NodeResult<Self> {
        // 创建节点信息
        let node_info = NodeInfo {
            node_id: config.node_id.clone(),
            node_type: config.node_type.clone(),
            listen_ip: config.listen_ip.clone(),
            listen_port: config.listen_port,
            region: config.region.clone(),
            capabilities: config.capabilities.clone(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            created_at: Utc::now(),
        };

        // 创建HTTP客户端（支持mTLS）
        let http_client = Self::create_http_client(&security_manager).await?;

        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            node_info: Arc::new(RwLock::new(node_info)),
            node_status: Arc::new(RwLock::new(NodeStatus::Starting)),
            storage_manager,
            security_manager,
            http_client,
            heartbeat_task: Arc::new(RwLock::new(None)),
            start_time: Instant::now(),
        })
    }

    /// 创建HTTP客户端
    async fn create_http_client(security_manager: &SecurityManager) -> NodeResult<reqwest::Client> {
        let cert_manager = security_manager.certificate_manager();
        let node_cert = cert_manager.get_node_certificate();
        let node_key = cert_manager.get_node_private_key();
        let ca_cert = cert_manager.get_ca_certificate();

        // 构建TLS配置
        let mut tls_config = rustls::ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates({
                let mut root_store = rustls::RootCertStore::empty();
                // 在实际实现中，这里应该解析CA证书并添加到root_store
                root_store
            })
            .with_no_client_auth(); // 简化实现，实际应该配置客户端证书

        let client = reqwest::Client::builder()
            .use_rustls_tls()
            .timeout(Duration::from_secs(30))
            .build()
            .map_err(|e| NodeError::ManagementServerConnectionFailed {
                reason: e.to_string(),
            })?;

        Ok(client)
    }

    /// 启动节点管理器
    pub async fn start(&self) -> NodeResult<()> {
        info!("启动节点管理器");

        // 更新节点状态
        self.set_status(NodeStatus::Starting).await;

        // 注册到管理服务器
        self.register_to_management_server().await?;

        // 启动心跳任务
        self.start_heartbeat_task().await;

        // 更新节点状态为在线
        self.set_status(NodeStatus::Online).await;

        info!("节点管理器启动完成");
        Ok(())
    }

    /// 停止节点管理器
    pub async fn stop(&self) -> NodeResult<()> {
        info!("停止节点管理器");

        // 停止心跳任务
        self.stop_heartbeat_task().await;

        // 更新节点状态为离线
        self.set_status(NodeStatus::Offline).await;

        // 向管理服务器发送下线通知
        if let Err(e) = self.notify_management_server_offline().await {
            warn!("通知管理服务器下线失败: {}", e);
        }

        info!("节点管理器停止完成");
        Ok(())
    }

    /// 注册到转发管理服务器
    pub async fn register_to_management_server(&self) -> NodeResult<()> {
        let config = self.config.read().await;
        let node_info = self.node_info.read().await;

        info!("向管理服务器注册节点: {}", config.management_server_url);

        let registration_request = NodeRegistrationRequest {
            node_info: node_info.clone(),
            timestamp: Utc::now(),
        };

        let url = format!("{}/api/v1/nodes/register", config.management_server_url);
        
        let response = self.http_client
            .post(&url)
            .json(&registration_request)
            .send()
            .await
            .map_err(|e| NodeError::RegistrationFailed {
                reason: e.to_string(),
            })?;

        if response.status().is_success() {
            let registration_response: NodeRegistrationResponse = response
                .json()
                .await
                .map_err(|e| NodeError::RegistrationFailed {
                    reason: format!("解析注册响应失败: {}", e),
                })?;

            info!("节点注册成功: {}", registration_response.message);
            
            // 应用全局配置更新
            if let Some(global_config) = registration_response.global_config {
                self.apply_global_config(global_config).await?;
            }

            Ok(())
        } else {
            let error_text = response.text().await.unwrap_or_else(|_| "未知错误".to_string());
            Err(NodeError::RegistrationFailed {
                reason: format!("注册失败: {} - {}", response.status(), error_text),
            })
        }
    }

    /// 启动心跳任务
    async fn start_heartbeat_task(&self) {
        let mut heartbeat_task = self.heartbeat_task.write().await;
        if heartbeat_task.is_some() {
            warn!("心跳任务已经在运行");
            return;
        }

        let config = self.config.clone();
        let node_info = self.node_info.clone();
        let node_status = self.node_status.clone();
        let storage_manager = self.storage_manager.clone();
        let http_client = self.http_client.clone();
        let start_time = self.start_time;

        let task = tokio::spawn(async move {
            let heartbeat_interval = {
                let config = config.read().await;
                Duration::from_secs(config.heartbeat_interval_seconds)
            };

            let mut interval = interval(heartbeat_interval);

            loop {
                interval.tick().await;

                if let Err(e) = Self::send_heartbeat(
                    &config,
                    &node_info,
                    &node_status,
                    &storage_manager,
                    &http_client,
                    start_time,
                ).await {
                    error!("发送心跳失败: {}", e);
                    
                    // 心跳失败时，将状态设置为降级
                    let mut status = node_status.write().await;
                    if *status == NodeStatus::Online {
                        *status = NodeStatus::Degraded;
                        warn!("心跳失败，节点状态降级");
                    }
                }
            }
        });

        *heartbeat_task = Some(task);
        info!("心跳任务已启动");
    }

    /// 停止心跳任务
    async fn stop_heartbeat_task(&self) {
        let mut heartbeat_task = self.heartbeat_task.write().await;
        if let Some(task) = heartbeat_task.take() {
            task.abort();
            info!("心跳任务已停止");
        }
    }

    /// 发送心跳
    async fn send_heartbeat(
        config: &Arc<RwLock<NodeConfig>>,
        node_info: &Arc<RwLock<NodeInfo>>,
        node_status: &Arc<RwLock<NodeStatus>>,
        storage_manager: &Arc<StorageManager>,
        http_client: &reqwest::Client,
        start_time: Instant,
    ) -> NodeResult<()> {
        let config = config.read().await;
        let node_info = node_info.read().await;
        let status = node_status.read().await.clone();

        // 收集负载信息
        let load_info = Self::collect_load_info(storage_manager, start_time).await;

        let heartbeat_request = HeartbeatRequest {
            node_id: node_info.node_id.clone(),
            timestamp: Utc::now(),
            status,
            load_info,
        };

        let url = format!("{}/api/v1/nodes/heartbeat", config.management_server_url);
        
        let response = http_client
            .post(&url)
            .json(&heartbeat_request)
            .send()
            .await
            .map_err(|e| NodeError::HeartbeatTimeout)?;

        if response.status().is_success() {
            let heartbeat_response: HeartbeatResponse = response
                .json()
                .await
                .map_err(|e| NodeError::HeartbeatTimeout)?;

            debug!("心跳发送成功");

            // 应用配置更新
            if let Some(config_updates) = heartbeat_response.config_updates {
                // 这里可以应用配置更新
                debug!("收到配置更新: {:?}", config_updates);
            }

            Ok(())
        } else {
            Err(NodeError::HeartbeatTimeout)
        }
    }

    /// 收集负载信息
    async fn collect_load_info(
        storage_manager: &Arc<StorageManager>,
        start_time: Instant,
    ) -> LoadInfo {
        let statistics = storage_manager.memory().get_statistics().await.unwrap_or_default();
        
        LoadInfo {
            cpu_usage: statistics.system.cpu_usage,
            memory_usage: statistics.system.memory_usage,
            disk_usage: statistics.system.disk_usage,
            network_io: NetworkIO {
                rx_bytes: statistics.network.bandwidth_in_mbps as u64 * 1024 * 1024,
                tx_bytes: statistics.network.bandwidth_out_mbps as u64 * 1024 * 1024,
            },
            session_count: statistics.sessions.active_sessions,
            uptime_seconds: start_time.elapsed().as_secs(),
        }
    }

    /// 应用全局配置
    async fn apply_global_config(&self, global_config: GlobalConfig) -> NodeResult<()> {
        info!("应用全局配置更新");
        
        // 更新心跳间隔
        if let Some(heartbeat_interval) = global_config.heartbeat_interval_seconds {
            let mut config = self.config.write().await;
            config.heartbeat_interval_seconds = heartbeat_interval;
            
            // 重启心跳任务以应用新的间隔
            drop(config);
            self.stop_heartbeat_task().await;
            self.start_heartbeat_task().await;
        }

        // 更新会话超时
        if let Some(session_timeout) = global_config.session_timeout_seconds {
            let mut config = self.config.write().await;
            config.session_timeout_seconds = session_timeout;
        }

        Ok(())
    }

    /// 通知管理服务器节点下线
    async fn notify_management_server_offline(&self) -> NodeResult<()> {
        let config = self.config.read().await;
        let node_info = self.node_info.read().await;

        let offline_request = NodeOfflineRequest {
            node_id: node_info.node_id.clone(),
            timestamp: Utc::now(),
            reason: "正常关闭".to_string(),
        };

        let url = format!("{}/api/v1/nodes/offline", config.management_server_url);
        
        let _response = self.http_client
            .post(&url)
            .json(&offline_request)
            .send()
            .await
            .map_err(|e| NodeError::ManagementServerConnectionFailed {
                reason: e.to_string(),
            })?;

        info!("已通知管理服务器节点下线");
        Ok(())
    }

    /// 设置节点状态
    async fn set_status(&self, status: NodeStatus) {
        let mut node_status = self.node_status.write().await;
        *node_status = status.clone();
        info!("节点状态更新为: {:?}", status);
    }

    /// 获取节点状态
    pub async fn get_status(&self) -> NodeStatus {
        self.node_status.read().await.clone()
    }

    /// 获取节点信息
    pub async fn get_node_info(&self) -> NodeInfo {
        self.node_info.read().await.clone()
    }

    /// 获取运行时间
    pub async fn get_uptime(&self) -> u64 {
        self.start_time.elapsed().as_secs()
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: NodeConfig) -> NodeResult<()> {
        let mut config = self.config.write().await;
        *config = new_config;
        info!("节点配置已更新");
        Ok(())
    }
}

/// 节点注册请求
#[derive(Debug, Serialize)]
struct NodeRegistrationRequest {
    node_info: NodeInfo,
    timestamp: DateTime<Utc>,
}

/// 节点注册响应
#[derive(Debug, Deserialize)]
struct NodeRegistrationResponse {
    success: bool,
    message: String,
    global_config: Option<GlobalConfig>,
}

/// 心跳请求
#[derive(Debug, Serialize)]
struct HeartbeatRequest {
    node_id: String,
    timestamp: DateTime<Utc>,
    status: NodeStatus,
    load_info: LoadInfo,
}

/// 心跳响应
#[derive(Debug, Deserialize)]
struct HeartbeatResponse {
    success: bool,
    timestamp: DateTime<Utc>,
    config_updates: Option<serde_json::Value>,
}

/// 节点下线请求
#[derive(Debug, Serialize)]
struct NodeOfflineRequest {
    node_id: String,
    timestamp: DateTime<Utc>,
    reason: String,
}

/// 负载信息
#[derive(Debug, Serialize)]
struct LoadInfo {
    cpu_usage: f32,
    memory_usage: f32,
    disk_usage: f32,
    network_io: NetworkIO,
    session_count: u32,
    uptime_seconds: u64,
}

/// 网络IO信息
#[derive(Debug, Serialize)]
struct NetworkIO {
    rx_bytes: u64,
    tx_bytes: u64,
}

/// 全局配置
#[derive(Debug, Deserialize)]
struct GlobalConfig {
    heartbeat_interval_seconds: Option<u64>,
    session_timeout_seconds: Option<u64>,
    log_level: Option<String>,
}
