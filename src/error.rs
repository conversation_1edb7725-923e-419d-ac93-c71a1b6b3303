// 转发节点错误定义
// 定义了转发节点中所有可能出现的错误类型

use thiserror::Error;

/// 转发节点主要错误类型
#[derive(Debug, Error)]
pub enum ForwarderError {
    #[error("节点管理错误: {0}")]
    NodeError(#[from] NodeError),
    
    #[error("会话管理错误: {0}")]
    SessionError(#[from] SessionError),
    
    #[error("传输错误: {0}")]
    TransportError(#[from] TransportError),
    
    #[error("连接错误: {0}")]
    ConnectionError(#[from] ConnectionError),
    
    #[error("配置错误: {0}")]
    ConfigError(#[from] ConfigError),
    
    #[error("网络错误: {0}")]
    NetworkError(#[from] NetworkError),
    
    #[error("安全错误: {0}")]
    SecurityError(#[from] SecurityError),
    
    #[error("存储错误: {0}")]
    StorageError(#[from] StorageError),
    
    #[error("MediaSoup错误: {0}")]
    MediaSoupError(String),
    
    #[error("内部错误: {0}")]
    InternalError(String),

    #[error("审计错误: {0}")]
    AuditError(#[from] AuditError),
}

/// 节点管理相关错误
#[derive(Debug, Error)]
pub enum NodeError {
    #[error("注册失败: {reason}")]
    RegistrationFailed { reason: String },
    
    #[error("心跳超时")]
    HeartbeatTimeout,
    
    #[error("配置无效: {field}")]
    InvalidConfig { field: String },
    
    #[error("资源不足: {resource}")]
    ResourceExhausted { resource: String },
    
    #[error("节点状态无效: {status}")]
    InvalidNodeStatus { status: String },
    
    #[error("管理服务器连接失败: {reason}")]
    ManagementServerConnectionFailed { reason: String },
}

/// 会话管理相关错误
#[derive(Debug, Error)]
pub enum SessionError {
    #[error("会话创建失败: {reason}")]
    CreationFailed { reason: String },
    
    #[error("会话不存在: {session_id}")]
    SessionNotFound { session_id: String },
    
    #[error("会话状态无效: {session_id}, 当前状态: {current_status}")]
    InvalidSessionStatus { session_id: String, current_status: String },
    
    #[error("会话配置无效: {field}")]
    InvalidSessionConfig { field: String },
    
    #[error("会话数量超过限制: {limit}")]
    SessionLimitExceeded { limit: u32 },
    
    #[error("设备ID无效: {device_id}")]
    InvalidDeviceId { device_id: String },

    #[error("会话管理器已在运行")]
    ManagerAlreadyRunning,

    #[error("设备会话数量超限: device_id={device_id}, limit={limit}, current={current}")]
    DeviceSessionLimitExceeded {
        device_id: String,
        limit: usize,
        current: usize,
    },

    #[error("配置无效: {reason}")]
    InvalidConfig { reason: String },

    #[error("存储错误: {source}")]
    StorageError {
        #[from]
        source: StorageError,
    },
}

/// 传输相关错误
#[derive(Debug, Error)]
pub enum TransportError {
    #[error("传输创建失败: {transport_type}, 原因: {reason}")]
    CreationFailed { transport_type: String, reason: String },
    
    #[error("传输不存在: {transport_id}")]
    TransportNotFound { transport_id: String },
    
    #[error("MediaSoup Worker创建失败: {reason}")]
    MediaSoupWorkerCreationFailed { reason: String },
    
    #[error("MediaSoup Router创建失败: {reason}")]
    MediaSoupRouterCreationFailed { reason: String },
    
    #[error("PipeTransport创建失败: {reason}")]
    PipeTransportCreationFailed { reason: String },
    
    #[error("PlainTransport创建失败: {reason}")]
    PlainTransportCreationFailed { reason: String },
    
    #[error("组播配置错误: {reason}")]
    MulticastConfigError { reason: String },
    
    #[error("端口分配失败: {port_range}")]
    PortAllocationFailed { port_range: String },
}

/// 连接管理相关错误
#[derive(Debug, Error)]
pub enum ConnectionError {
    #[error("TCP隧道创建失败: {reason}")]
    TcpTunnelCreationFailed { reason: String },
    
    #[error("UDP隧道创建失败: {reason}")]
    UdpTunnelCreationFailed { reason: String },
    
    #[error("隧道不存在: {tunnel_id}")]
    TunnelNotFound { tunnel_id: String },
    
    #[error("连接池已满")]
    ConnectionPoolFull,
    
    #[error("连接超时: {timeout_ms}ms")]
    ConnectionTimeout { timeout_ms: u64 },
    
    #[error("连接断开: {reason}")]
    ConnectionLost { reason: String },
}

/// 配置相关错误
#[derive(Debug, Error)]
pub enum ConfigError {
    #[error("配置文件不存在: {path}")]
    ConfigFileNotFound { path: String },
    
    #[error("配置文件格式错误: {reason}")]
    ConfigFormatError { reason: String },
    
    #[error("配置验证失败: {field}, 原因: {reason}")]
    ConfigValidationFailed { field: String, reason: String },
    
    #[error("配置更新失败: {reason}")]
    ConfigUpdateFailed { reason: String },
}

/// 网络相关错误
#[derive(Debug, Error)]
pub enum NetworkError {
    #[error("网络连接失败: {address}")]
    ConnectionFailed { address: String },
    
    #[error("DNS解析失败: {hostname}")]
    DnsResolutionFailed { hostname: String },
    
    #[error("端口绑定失败: {port}")]
    PortBindFailed { port: u16 },
    
    #[error("网络超时: {timeout_ms}ms")]
    NetworkTimeout { timeout_ms: u64 },
    
    #[error("数据传输错误: {reason}")]
    DataTransferError { reason: String },
}

/// 安全相关错误
#[derive(Debug, Error)]
pub enum SecurityError {
    #[error("证书无效")]
    InvalidCertificate,
    
    #[error("证书已过期")]
    CertificateExpired,
    
    #[error("证书验证失败: {reason}")]
    CertificateVerificationFailed { reason: String },
    
    #[error("权限不足")]
    InsufficientPermissions,
    
    #[error("未授权的API访问")]
    UnauthorizedApiAccess,
    
    #[error("节点不活跃")]
    NodeInactive,
    
    #[error("未授权的端点")]
    UnauthorizedEndpoint,
    
    #[error("速率限制超出")]
    RateLimitExceeded,
    
    #[error("调用方不受信任")]
    CallerNotTrusted,

    #[error("审计错误: {0}")]
    AuditError(#[from] AuditError),
}

/// 存储相关错误
#[derive(Debug, Error)]
pub enum StorageError {
    #[error("数据序列化失败: {reason}")]
    SerializationFailed { reason: String },
    
    #[error("数据反序列化失败: {reason}")]
    DeserializationFailed { reason: String },
    
    #[error("文件操作失败: {operation}, 文件: {path}, 原因: {reason}")]
    FileOperationFailed { operation: String, path: String, reason: String },
    
    #[error("内存不足")]
    OutOfMemory,
    
    #[error("存储空间不足")]
    OutOfDiskSpace,
    
    #[error("数据损坏: {data_type}")]
    DataCorruption { data_type: String },
}

/// 审计相关错误
#[derive(Debug, Error)]
pub enum AuditError {
    #[error("审计日志写入失败: {reason}")]
    AuditLogWriteFailed { reason: String },
    
    #[error("事件存储失败: {reason}")]
    EventStoreFailed { reason: String },
    
    #[error("告警发送失败: {reason}")]
    AlertSendFailed { reason: String },
}

/// 结果类型别名
pub type Result<T> = std::result::Result<T, ForwarderError>;
pub type NodeResult<T> = std::result::Result<T, NodeError>;
pub type SessionResult<T> = std::result::Result<T, SessionError>;
pub type TransportResult<T> = std::result::Result<T, TransportError>;
pub type ConnectionResult<T> = std::result::Result<T, ConnectionError>;
pub type ConfigResult<T> = std::result::Result<T, ConfigError>;
pub type NetworkResult<T> = std::result::Result<T, NetworkError>;
pub type SecurityResult<T> = std::result::Result<T, SecurityError>;
pub type StorageResult<T> = std::result::Result<T, StorageError>;
pub type AuditResult<T> = std::result::Result<T, AuditError>;
