// KVM隧道转发节点库
// 提供转发节点的核心功能

pub mod config;
pub mod core;
pub mod error;
pub mod network;
pub mod security;
pub mod storage;
pub mod monitoring;
pub mod types;

// 重新导出主要的公共接口
pub use core::ForwarderNode;
pub use config::ForwarderConfig;
pub use error::{ForwarderError, Result};

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{NodeType, NodeCapabilities};
    
    #[test]
    fn test_node_capabilities_creation() {
        let capabilities = NodeCapabilities {
            max_connections: 1000,
            max_bandwidth_mbps: 1000,
            supported_codecs: vec!["H264".to_string(), "VP8".to_string()],
            supported_protocols: vec!["TCP".to_string(), "UDP".to_string()],
            supports_mediasoup: true,
            supports_tcp_tunnel: true,
            supports_udp_tunnel: true,
            supports_multicast: true,
        };
        
        assert_eq!(capabilities.max_connections, 1000);
        assert_eq!(capabilities.max_bandwidth_mbps, 1000);
        assert!(capabilities.supports_mediasoup);
        assert!(capabilities.supports_tcp_tunnel);
        assert!(capabilities.supports_udp_tunnel);
        assert!(capabilities.supports_multicast);
    }
    
    #[test]
    fn test_node_type_serialization() {
        let forwarder_type = NodeType::Forwarder;
        let management_type = NodeType::Management;
        
        // 测试序列化
        let forwarder_json = serde_json::to_string(&forwarder_type).unwrap();
        let management_json = serde_json::to_string(&management_type).unwrap();
        
        assert!(forwarder_json.contains("Forwarder"));
        assert!(management_json.contains("Management"));
        
        // 测试反序列化
        let deserialized_forwarder: NodeType = serde_json::from_str(&forwarder_json).unwrap();
        let deserialized_management: NodeType = serde_json::from_str(&management_json).unwrap();
        
        assert_eq!(deserialized_forwarder, NodeType::Forwarder);
        assert_eq!(deserialized_management, NodeType::Management);
    }
    
    #[test]
    fn test_error_types() {
        use crate::error::{NodeError, SessionError, TransportError};
        
        let node_error = NodeError::RegistrationFailed {
            reason: "测试错误".to_string(),
        };
        
        let session_error = SessionError::CreationFailed {
            reason: "测试错误".to_string(),
        };
        
        let transport_error = TransportError::CreationFailed {
            transport_type: "TCP".to_string(),
            reason: "测试错误".to_string(),
        };
        
        // 测试错误消息
        assert!(node_error.to_string().contains("注册失败"));
        assert!(session_error.to_string().contains("会话创建失败"));
        assert!(transport_error.to_string().contains("传输创建失败"));
    }
    
    #[test]
    fn test_id_generation() {
        use crate::types::{generate_id, generate_session_id, generate_transport_id, SessionType, TransportType};
        
        // 测试基础ID生成
        let id1 = generate_id();
        let id2 = generate_id();
        assert_ne!(id1, id2);
        assert!(!id1.is_empty());
        assert!(!id2.is_empty());
        
        // 测试会话ID生成
        let tx_session_id = generate_session_id(&SessionType::Transmitter);
        let rx_session_id = generate_session_id(&SessionType::Receiver);
        
        assert!(tx_session_id.starts_with("sess_tx_"));
        assert!(rx_session_id.starts_with("sess_rx_"));
        
        // 测试传输ID生成
        let tcp_transport_id = generate_transport_id(&TransportType::TcpTunnel);
        let udp_transport_id = generate_transport_id(&TransportType::UdpTunnel);
        
        assert!(tcp_transport_id.starts_with("tcp_"));
        assert!(udp_transport_id.starts_with("udp_"));
    }
}
