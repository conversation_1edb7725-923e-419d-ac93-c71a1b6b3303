// 转发节点核心数据结构定义
// 包含节点信息、会话信息、传输配置等核心数据类型

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::net::IpAddr;
use uuid::Uuid;

/// 节点ID类型
pub type NodeId = String;
/// 会话ID类型
pub type SessionId = String;
/// 传输ID类型
pub type TransportId = String;
/// 隧道ID类型
pub type TunnelId = String;
/// 连接ID类型
pub type ConnectionId = String;
/// 管道ID类型
pub type PipeId = String;

/// 节点信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeInfo {
    /// 节点唯一标识
    pub node_id: NodeId,
    /// 节点类型
    pub node_type: NodeType,
    /// 监听IP地址
    pub listen_ip: String,
    /// 监听端口
    pub listen_port: u16,
    /// 所属区域
    pub region: String,
    /// 节点能力
    pub capabilities: NodeCapabilities,
    /// 版本信息
    pub version: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 节点类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NodeType {
    /// 转发节点
    Forwarder,
    /// 管理节点
    Management,
}

/// 节点能力
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeCapabilities {
    /// 最大连接数
    pub max_connections: u32,
    /// 最大带宽(Mbps)
    pub max_bandwidth_mbps: u32,
    /// 支持的编解码器
    pub supported_codecs: Vec<String>,
    /// 支持的协议
    pub supported_protocols: Vec<String>,
    /// 是否支持MediaSoup
    pub supports_mediasoup: bool,
    /// 是否支持TCP隧道
    pub supports_tcp_tunnel: bool,
    /// 是否支持UDP隧道
    pub supports_udp_tunnel: bool,
    /// 是否支持组播
    pub supports_multicast: bool,
}

/// 节点状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NodeStatus {
    /// 启动中
    Starting,
    /// 在线
    Online,
    /// 降级服务
    Degraded,
    /// 离线
    Offline,
    /// 维护中
    Maintenance,
}

/// 会话信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionInfo {
    /// 会话ID
    pub session_id: SessionId,
    /// 会话类型
    pub session_type: SessionType,
    /// 会话状态
    pub status: SessionStatus,
    /// 设备ID
    pub device_id: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 会话配置
    pub config: SessionConfig,
    /// 会话统计
    pub stats: SessionStats,
}

/// 会话类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SessionType {
    /// 发送端(KVM TX设备)
    Transmitter,
    /// 接收端(KVM RX设备)
    Receiver,
    /// 中继
    Relay,
}

/// 会话状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SessionStatus {
    /// 初始化中
    Initializing,
    /// 配置中
    Configuring,
    /// 连接中
    Connecting,
    /// 活跃
    Active,
    /// 暂停
    Paused,
    /// 降级
    Degraded,
    /// 失败
    Failed,
    /// 已终止
    Terminated,
}

/// 会话配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionConfig {
    /// 会话类型
    pub session_type: SessionType,
    /// 设备ID
    pub device_id: String,
    /// 传输配置列表
    pub transports: Vec<TransportConfig>,
    /// 安全配置
    pub security: Option<SecurityConfig>,
    /// QoS配置
    pub qos: Option<QosConfig>,
}

/// 会话统计信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SessionStats {
    /// 发送字节数
    pub bytes_sent: u64,
    /// 接收字节数
    pub bytes_received: u64,
    /// 发送包数
    pub packets_sent: u64,
    /// 接收包数
    pub packets_received: u64,
    /// 丢包率
    pub packet_loss_rate: f32,
    /// 往返时延(毫秒)
    pub rtt_ms: u32,
    /// 带宽使用率(Mbps)
    pub bandwidth_usage_mbps: f32,
    /// 活跃连接数
    pub active_connections: u32,
}

/// 传输配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransportConfig {
    /// 传输类型
    pub transport_type: TransportType,
    /// 地址配置
    pub addresses: Vec<AddressConfig>,
    /// 安全配置
    pub security: Option<SecurityConfig>,
    /// QoS配置
    pub qos: Option<QosConfig>,
}

/// 传输类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TransportType {
    /// MediaSoup传输
    MediaSoup,
    /// 端口转发
    PortForward,
    /// 组播
    Multicast,
    /// WebRTC
    WebRTC,
    /// TCP隧道
    TcpTunnel,
    /// UDP隧道
    UdpTunnel,
}

/// 地址配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AddressConfig {
    /// IP地址
    pub ip: String,
    /// 端口范围
    pub port: PortRange,
    /// 协议类型
    pub protocol: Protocol,
}

/// 端口范围
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PortRange {
    /// 单个端口
    Single(u16),
    /// 端口范围
    Range(u16, u16),
    /// 端口列表
    List(Vec<u16>),
}

/// 协议类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum Protocol {
    /// TCP协议
    TCP,
    /// UDP协议
    UDP,
    /// 组播协议
    Multicast,
    /// WebRTC协议
    WebRTC,
}

/// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// 是否启用加密
    pub encryption_enabled: bool,
    /// 加密套件
    pub cipher_suite: Option<String>,
    /// 密钥
    pub key: Option<String>,
}

/// QoS配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QosConfig {
    /// 优先级
    pub priority: u8,
    /// 最大带宽(Mbps)
    pub max_bandwidth_mbps: Option<u32>,
    /// 最大延迟(毫秒)
    pub max_latency_ms: Option<u32>,
}

/// 会话摘要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionSummary {
    /// 会话ID
    pub session_id: SessionId,
    /// 会话类型
    pub session_type: SessionType,
    /// 会话状态
    pub status: SessionStatus,
    /// 设备ID
    pub device_id: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 活跃连接数
    pub active_connections: u32,
}

impl From<&SessionInfo> for SessionSummary {
    fn from(session: &SessionInfo) -> Self {
        Self {
            session_id: session.session_id.clone(),
            session_type: session.session_type.clone(),
            status: session.status.clone(),
            device_id: session.device_id.clone(),
            created_at: session.created_at,
            active_connections: session.stats.active_connections,
        }
    }
}

/// 传输信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransportInfo {
    /// 传输ID
    pub transport_id: TransportId,
    /// 传输类型
    pub transport_type: TransportType,
    /// 传输状态
    pub status: TransportStatus,
    /// 配置信息
    pub config: TransportConfig,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 统计信息
    pub stats: TransportStats,
}

/// 传输状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TransportStatus {
    /// 初始化中
    Initializing,
    /// 活跃
    Active,
    /// 暂停
    Paused,
    /// 错误
    Error,
    /// 已关闭
    Closed,
}

/// 传输统计信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TransportStats {
    /// 发送字节数
    pub bytes_sent: u64,
    /// 接收字节数
    pub bytes_received: u64,
    /// 发送包数
    pub packets_sent: u64,
    /// 接收包数
    pub packets_received: u64,
    /// 错误计数
    pub error_count: u32,
}

/// 生成新的UUID字符串
pub fn generate_id() -> String {
    Uuid::new_v4().to_string()
}

/// 生成会话ID
pub fn generate_session_id(session_type: &SessionType) -> SessionId {
    let prefix = match session_type {
        SessionType::Transmitter => "sess_tx",
        SessionType::Receiver => "sess_rx",
        SessionType::Relay => "sess_relay",
    };
    format!("{}_{}", prefix, generate_id())
}

/// 生成传输ID
pub fn generate_transport_id(transport_type: &TransportType) -> TransportId {
    let prefix = match transport_type {
        TransportType::MediaSoup => "ms",
        TransportType::PortForward => "pf",
        TransportType::Multicast => "mc",
        TransportType::WebRTC => "webrtc",
        TransportType::TcpTunnel => "tcp",
        TransportType::UdpTunnel => "udp",
    };
    format!("{}_{}", prefix, generate_id())
}

/// 节点间连接信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InterNodeConnection {
    /// 连接ID
    pub connection_id: ConnectionId,
    /// 源节点ID
    pub source_node: NodeId,
    /// 目标节点ID
    pub target_node: NodeId,
    /// 连接状态
    pub status: ConnectionStatus,
    /// 连接类型
    pub connection_type: ConnectionType,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后活跃时间
    pub last_active: DateTime<Utc>,
    /// 连接统计
    pub stats: ConnectionStats,
}

/// 连接状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConnectionStatus {
    /// 连接中
    Connecting,
    /// 已连接
    Connected,
    /// 断开连接
    Disconnected,
    /// 错误
    Error,
}

/// 连接类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConnectionType {
    /// PipeTransport连接
    PipeTransport,
    /// TCP隧道连接
    TcpTunnel,
    /// UDP隧道连接
    UdpTunnel,
    /// 管理连接
    Management,
}

/// 连接统计信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ConnectionStats {
    /// 传输字节数
    pub bytes_transferred: u64,
    /// 发送包数
    pub packets_sent: u64,
    /// 丢包率
    pub packet_loss_rate: f32,
    /// 延迟(毫秒)
    pub latency_ms: u32,
    /// 带宽使用率(Mbps)
    pub bandwidth_mbps: f32,
}

/// 隧道信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TunnelInfo {
    /// 隧道ID
    pub tunnel_id: TunnelId,
    /// 隧道类型
    pub tunnel_type: TunnelType,
    /// 隧道状态
    pub status: TunnelStatus,
    /// 本地地址
    pub local_address: String,
    /// 本地端口
    pub local_port: u16,
    /// 远程地址
    pub remote_address: Option<String>,
    /// 远程端口
    pub remote_port: Option<u16>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 统计信息
    pub stats: TunnelStats,
}

/// 隧道类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TunnelType {
    /// TCP隧道
    Tcp,
    /// UDP隧道
    Udp,
}

/// 隧道状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TunnelStatus {
    /// 监听中
    Listening,
    /// 已连接
    Connected,
    /// 已关闭
    Closed,
    /// 错误
    Error,
}

/// 隧道统计信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TunnelStats {
    /// 活跃连接数
    pub active_connections: u32,
    /// 总连接数
    pub total_connections: u32,
    /// 传输字节数
    pub bytes_transferred: u64,
    /// 错误计数
    pub error_count: u32,
}

/// 节点统计信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct NodeStatistics {
    /// 系统指标
    pub system: SystemMetrics,
    /// 网络指标
    pub network: NetworkMetrics,
    /// 会话指标
    pub sessions: SessionMetrics,
    /// 性能指标
    pub performance: PerformanceMetrics,
    /// 安全指标
    pub security: SecurityMetrics,
}

/// 系统指标
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SystemMetrics {
    /// CPU使用率(%)
    pub cpu_usage: f32,
    /// 内存使用率(%)
    pub memory_usage: f32,
    /// 磁盘使用率(%)
    pub disk_usage: f32,
    /// 运行时间(秒)
    pub uptime_seconds: u64,
    /// 负载平均值
    pub load_average: f32,
}

/// 网络指标
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct NetworkMetrics {
    /// 入站带宽(Mbps)
    pub bandwidth_in_mbps: f32,
    /// 出站带宽(Mbps)
    pub bandwidth_out_mbps: f32,
    /// 丢包率
    pub packet_loss_rate: f32,
    /// 连接数
    pub connection_count: u32,
    /// 网络延迟(毫秒)
    pub network_latency_ms: f32,
}

/// 会话指标
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SessionMetrics {
    /// 总会话数
    pub total_sessions: u32,
    /// 活跃会话数
    pub active_sessions: u32,
    /// 失败会话数
    pub failed_sessions: u32,
    /// 平均会话持续时间(秒)
    pub average_session_duration: f32,
}

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct PerformanceMetrics {
    /// 请求延迟(毫秒)
    pub request_latency_ms: f32,
    /// 令牌验证延迟(毫秒)
    pub token_verification_latency_ms: f32,
    /// 会话创建延迟(毫秒)
    pub session_creation_latency_ms: f32,
    /// 吞吐量(Mbps)
    pub throughput_mbps: f32,
}

/// 安全指标
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SecurityMetrics {
    /// 证书验证次数
    pub certificate_verifications: u32,
    /// 证书验证失败次数
    pub certificate_verification_failures: u32,
    /// 管理服务器调用次数
    pub management_server_calls: u32,
    /// 管理服务器调用失败次数
    pub management_server_call_failures: u32,
    /// 节点间调用次数
    pub inter_node_calls: u32,
    /// 节点间调用失败次数
    pub inter_node_call_failures: u32,
    /// 速率限制请求数
    pub rate_limited_requests: u32,
    /// 不受信任的调用方数
    pub untrusted_callers: u32,
}
