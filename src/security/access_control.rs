// 访问控制模块
// 负责API权限控制和访问策略管理

use std::collections::HashMap;
use serde::{Deserialize, Serialize};

use crate::config::ApiPermissionConfig;
use crate::error::{SecurityError, SecurityResult};

/// 访问控制管理器
pub struct AccessControlManager {
    /// API权限配置
    api_permissions: ApiPermissionConfig,
    /// API权限映射
    permission_map: HashMap<String, ApiCategory>,
}

impl AccessControlManager {
    /// 创建新的访问控制管理器
    pub fn new(config: ApiPermissionConfig) -> Self {
        let mut permission_map = HashMap::new();

        // 构建权限映射
        for api in &config.management_only_apis {
            permission_map.insert(api.clone(), ApiCategory::ManagementOnly);
        }

        for api in &config.inter_node_only_apis {
            permission_map.insert(api.clone(), ApiCategory::InterNodeOnly);
        }

        for api in &config.both_callable_apis {
            permission_map.insert(api.clone(), ApiCategory::Both);
        }

        Self {
            api_permissions: config,
            permission_map,
        }
    }

    /// 检查API权限
    pub fn check_api_permission(
        &self,
        path: &str,
        method: &HttpMethod,
        caller_type: &CallerType,
    ) -> SecurityResult<()> {
        let api_key = format!("{} {}", method.as_str(), path);
        
        // 查找精确匹配
        if let Some(category) = self.permission_map.get(&api_key) {
            return self.check_permission_category(category, caller_type);
        }

        // 查找模式匹配
        for (pattern, category) in &self.permission_map {
            if self.match_api_pattern(pattern, &api_key) {
                return self.check_permission_category(category, caller_type);
            }
        }

        // 默认拒绝访问
        Err(SecurityError::UnauthorizedApiAccess)
    }

    /// 检查权限类别
    fn check_permission_category(
        &self,
        category: &ApiCategory,
        caller_type: &CallerType,
    ) -> SecurityResult<()> {
        match (category, caller_type) {
            (ApiCategory::ManagementOnly, CallerType::ManagementServer) => Ok(()),
            (ApiCategory::InterNodeOnly, CallerType::ForwarderNode) => Ok(()),
            (ApiCategory::Both, _) => Ok(()),
            _ => Err(SecurityError::UnauthorizedApiAccess),
        }
    }

    /// 匹配API模式
    fn match_api_pattern(&self, pattern: &str, api_key: &str) -> bool {
        // 支持通配符匹配
        if pattern.ends_with("*") {
            let prefix = &pattern[..pattern.len() - 1];
            api_key.starts_with(prefix)
        } else if pattern.contains("*") {
            // 支持中间通配符
            let parts: Vec<&str> = pattern.split('*').collect();
            if parts.len() == 2 {
                api_key.starts_with(parts[0]) && api_key.ends_with(parts[1])
            } else {
                false
            }
        } else {
            pattern == api_key
        }
    }

    /// 检查是否为节点间API
    pub fn is_inter_node_api(&self, path: &str) -> bool {
        // 检查是否为节点间专用API
        for api in &self.api_permissions.inter_node_only_apis {
            if self.match_api_pattern(api, path) {
                return true;
            }
        }

        // 检查是否为通用API
        for api in &self.api_permissions.both_callable_apis {
            if self.match_api_pattern(api, path) {
                return true;
            }
        }

        false
    }

    /// 检查是否为管理API
    pub fn is_management_api(&self, path: &str) -> bool {
        // 检查是否为管理专用API
        for api in &self.api_permissions.management_only_apis {
            if self.match_api_pattern(api, path) {
                return true;
            }
        }

        // 检查是否为通用API
        for api in &self.api_permissions.both_callable_apis {
            if self.match_api_pattern(api, path) {
                return true;
            }
        }

        false
    }

    /// 获取API权限列表
    pub fn get_api_permissions(&self, caller_type: &CallerType) -> Vec<String> {
        let mut permissions = Vec::new();

        match caller_type {
            CallerType::ManagementServer => {
                permissions.extend(self.api_permissions.management_only_apis.clone());
                permissions.extend(self.api_permissions.both_callable_apis.clone());
            }
            CallerType::ForwarderNode => {
                permissions.extend(self.api_permissions.inter_node_only_apis.clone());
                permissions.extend(self.api_permissions.both_callable_apis.clone());
            }
        }

        permissions.sort();
        permissions.dedup();
        permissions
    }

    /// 更新API权限配置
    pub fn update_permissions(&mut self, config: ApiPermissionConfig) {
        self.api_permissions = config;
        
        // 重新构建权限映射
        self.permission_map.clear();
        
        for api in &self.api_permissions.management_only_apis {
            self.permission_map.insert(api.clone(), ApiCategory::ManagementOnly);
        }

        for api in &self.api_permissions.inter_node_only_apis {
            self.permission_map.insert(api.clone(), ApiCategory::InterNodeOnly);
        }

        for api in &self.api_permissions.both_callable_apis {
            self.permission_map.insert(api.clone(), ApiCategory::Both);
        }
    }

    /// 验证API路径格式
    pub fn validate_api_path(&self, path: &str) -> bool {
        // 检查路径格式
        if !path.starts_with('/') {
            return false;
        }

        // 检查路径长度
        if path.len() > 200 {
            return false;
        }

        // 检查是否包含非法字符
        for ch in path.chars() {
            if !ch.is_ascii() || ch.is_control() {
                return false;
            }
        }

        true
    }
}

/// API权限类别
#[derive(Debug, Clone, PartialEq)]
pub enum ApiCategory {
    /// 仅管理服务器可调用
    ManagementOnly,
    /// 仅节点间可调用
    InterNodeOnly,
    /// 两者都可调用
    Both,
}

/// 调用方类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CallerType {
    /// 管理服务器
    ManagementServer,
    /// 转发节点
    ForwarderNode,
}

/// HTTP方法
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum HttpMethod {
    GET,
    POST,
    PUT,
    DELETE,
    PATCH,
    HEAD,
    OPTIONS,
}

impl HttpMethod {
    /// 从字符串创建HTTP方法
    pub fn from_str(method: &str) -> Option<Self> {
        match method.to_uppercase().as_str() {
            "GET" => Some(Self::GET),
            "POST" => Some(Self::POST),
            "PUT" => Some(Self::PUT),
            "DELETE" => Some(Self::DELETE),
            "PATCH" => Some(Self::PATCH),
            "HEAD" => Some(Self::HEAD),
            "OPTIONS" => Some(Self::OPTIONS),
            _ => None,
        }
    }

    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::GET => "GET",
            Self::POST => "POST",
            Self::PUT => "PUT",
            Self::DELETE => "DELETE",
            Self::PATCH => "PATCH",
            Self::HEAD => "HEAD",
            Self::OPTIONS => "OPTIONS",
        }
    }

    /// 从路径提取HTTP方法（简化实现）
    pub fn from_path(path: &str) -> Self {
        // 简化实现，实际应该从HTTP请求头获取
        if path.contains("/sessions/") && path.ends_with("/delete") {
            Self::DELETE
        } else if path.contains("/sessions/") {
            Self::POST
        } else {
            Self::GET
        }
    }
}

/// 访问控制规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessRule {
    /// 规则ID
    pub rule_id: String,
    /// 规则名称
    pub name: String,
    /// API模式
    pub api_pattern: String,
    /// 允许的调用方类型
    pub allowed_callers: Vec<CallerType>,
    /// 是否启用
    pub enabled: bool,
    /// 规则描述
    pub description: String,
}

/// 访问控制列表
#[derive(Debug, Clone)]
pub struct AccessControlList {
    /// 访问规则列表
    rules: Vec<AccessRule>,
    /// 默认策略
    default_policy: DefaultPolicy,
}

impl AccessControlList {
    /// 创建新的访问控制列表
    pub fn new(default_policy: DefaultPolicy) -> Self {
        Self {
            rules: Vec::new(),
            default_policy,
        }
    }

    /// 添加访问规则
    pub fn add_rule(&mut self, rule: AccessRule) {
        self.rules.push(rule);
    }

    /// 移除访问规则
    pub fn remove_rule(&mut self, rule_id: &str) {
        self.rules.retain(|rule| rule.rule_id != rule_id);
    }

    /// 检查访问权限
    pub fn check_access(&self, api_path: &str, caller_type: &CallerType) -> bool {
        // 检查规则
        for rule in &self.rules {
            if rule.enabled && self.match_pattern(&rule.api_pattern, api_path) {
                return rule.allowed_callers.contains(caller_type);
            }
        }

        // 应用默认策略
        match self.default_policy {
            DefaultPolicy::Allow => true,
            DefaultPolicy::Deny => false,
        }
    }

    /// 匹配模式
    fn match_pattern(&self, pattern: &str, path: &str) -> bool {
        if pattern.ends_with("*") {
            let prefix = &pattern[..pattern.len() - 1];
            path.starts_with(prefix)
        } else {
            pattern == path
        }
    }

    /// 获取规则列表
    pub fn get_rules(&self) -> &[AccessRule] {
        &self.rules
    }

    /// 更新默认策略
    pub fn set_default_policy(&mut self, policy: DefaultPolicy) {
        self.default_policy = policy;
    }
}

/// 默认策略
#[derive(Debug, Clone, PartialEq)]
pub enum DefaultPolicy {
    /// 默认允许
    Allow,
    /// 默认拒绝
    Deny,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_pattern_matching() {
        let config = ApiPermissionConfig {
            management_only_apis: vec![
                "POST /api/v1/sessions/*".to_string(),
                "DELETE /api/v1/sessions/*".to_string(),
            ],
            inter_node_only_apis: vec![
                "POST /api/v1/inter-node/*".to_string(),
            ],
            both_callable_apis: vec![
                "GET /api/v1/status".to_string(),
                "GET /health".to_string(),
            ],
        };

        let manager = AccessControlManager::new(config);

        // 测试管理API
        assert!(manager.check_api_permission(
            "/api/v1/sessions/transmitter",
            &HttpMethod::POST,
            &CallerType::ManagementServer
        ).is_ok());

        assert!(manager.check_api_permission(
            "/api/v1/sessions/transmitter",
            &HttpMethod::POST,
            &CallerType::ForwarderNode
        ).is_err());

        // 测试节点间API
        assert!(manager.check_api_permission(
            "/api/v1/inter-node/access-request",
            &HttpMethod::POST,
            &CallerType::ForwarderNode
        ).is_ok());

        assert!(manager.check_api_permission(
            "/api/v1/inter-node/access-request",
            &HttpMethod::POST,
            &CallerType::ManagementServer
        ).is_err());

        // 测试通用API
        assert!(manager.check_api_permission(
            "/api/v1/status",
            &HttpMethod::GET,
            &CallerType::ManagementServer
        ).is_ok());

        assert!(manager.check_api_permission(
            "/api/v1/status",
            &HttpMethod::GET,
            &CallerType::ForwarderNode
        ).is_ok());
    }

    #[test]
    fn test_http_method_parsing() {
        assert_eq!(HttpMethod::from_str("GET"), Some(HttpMethod::GET));
        assert_eq!(HttpMethod::from_str("post"), Some(HttpMethod::POST));
        assert_eq!(HttpMethod::from_str("INVALID"), None);
    }

    #[test]
    fn test_api_path_validation() {
        let config = ApiPermissionConfig {
            management_only_apis: vec![],
            inter_node_only_apis: vec![],
            both_callable_apis: vec![],
        };

        let manager = AccessControlManager::new(config);

        assert!(manager.validate_api_path("/api/v1/status"));
        assert!(manager.validate_api_path("/health"));
        assert!(!manager.validate_api_path("invalid-path"));
        assert!(!manager.validate_api_path("/api/v1/very-long-path-that-exceeds-the-maximum-allowed-length-for-api-paths-and-should-be-rejected-by-the-validation-function-because-it-is-too-long-and-might-cause-security-issues"));
    }
}
