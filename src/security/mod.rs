// 安全管理模块
// 提供mTLS双向认证、证书管理、访问控制和安全审计功能

pub mod certificate;
pub mod access_control;
pub mod audit;
pub mod rate_limit;

use std::sync::Arc;
use std::collections::HashMap;
use std::net::IpAddr;
use dashmap::DashMap;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use async_trait::async_trait;

use crate::config::SecurityConfig;
use crate::error::{SecurityError, SecurityResult};

/// 安全管理器
pub struct SecurityManager {
    /// 证书管理器
    cert_manager: Arc<certificate::CertificateManager>,
    
    /// 访问控制管理器
    access_control: Arc<access_control::AccessControlManager>,
    
    /// 安全审计器
    auditor: Arc<audit::SecurityAuditor>,
    
    /// 速率限制器
    rate_limiter: Arc<rate_limit::RateLimiter>,
    
    /// 信任的节点列表
    trusted_nodes: Arc<DashMap<String, TrustedNode>>,
    
    /// 信任的管理服务器列表
    trusted_servers: Arc<DashMap<String, TrustedServer>>,
}

impl SecurityManager {
    /// 创建新的安全管理器
    pub async fn new(config: SecurityConfig) -> SecurityResult<Self> {
        let cert_manager = Arc::new(certificate::CertificateManager::new(
            &config.node_certificate_path,
            &config.node_private_key_path,
            &config.ca_certificate_path,
        ).await?);

        let access_control = Arc::new(access_control::AccessControlManager::new(
            config.api_permissions.clone()
        ));

        let auditor = Arc::new(audit::SecurityAuditor::new().await?);

        let rate_limiter = Arc::new(rate_limit::RateLimiter::new(
            config.rate_limits.clone()
        ));

        let trusted_nodes = Arc::new(DashMap::new());
        let trusted_servers = Arc::new(DashMap::new());

        // 初始化信任的服务器列表
        for server in config.trusted_servers {
            trusted_servers.insert(server.server_id.clone(), TrustedServer {
                server_id: server.server_id,
                server_name: server.server_name,
                certificate_fingerprint: server.certificate_fingerprint,
                allowed_apis: server.allowed_apis,
                is_active: server.is_active,
                last_seen: Utc::now(),
            });
        }

        Ok(Self {
            cert_manager,
            access_control,
            auditor,
            rate_limiter,
            trusted_nodes,
            trusted_servers,
        })
    }

    /// 验证管理服务器调用
    pub async fn verify_management_call(
        &self,
        client_cert: &certificate::Certificate,
        request_path: &str,
        source_ip: IpAddr,
    ) -> SecurityResult<()> {
        // 1. 验证客户端证书
        let cert_info = self.cert_manager.verify_certificate(client_cert).await?;
        
        // 2. 检查证书指纹是否为信任的管理服务器
        let server_info = self.find_trusted_server(&cert_info.fingerprint)?;
        
        // 3. 检查服务器是否活跃
        if !server_info.is_active {
            let event = audit::SecurityEvent {
                event_id: uuid::Uuid::new_v4().to_string(),
                event_type: audit::SecurityEventType::AccessDenied,
                timestamp: Utc::now(),
                caller_info: audit::CallerInfo {
                    caller_id: server_info.server_id.clone(),
                    caller_type: audit::CallerType::ManagementServer,
                    source_ip,
                    certificate_fingerprint: Some(cert_info.fingerprint.clone()),
                },
                api_path: request_path.to_string(),
                action: "management_call".to_string(),
                result: audit::SecurityEventResult::Failure("服务器不活跃".to_string()),
                details: serde_json::json!({
                    "server_name": server_info.server_name,
                    "is_active": server_info.is_active
                }),
            };
            self.auditor.record_event(event).await?;
            
            return Err(SecurityError::NodeInactive);
        }
        
        // 4. 检查API路径权限
        if !server_info.allowed_apis.iter().any(|api| self.match_api_pattern(api, request_path)) {
            let event = audit::SecurityEvent {
                event_id: uuid::Uuid::new_v4().to_string(),
                event_type: audit::SecurityEventType::UnauthorizedApiCall,
                timestamp: Utc::now(),
                caller_info: audit::CallerInfo {
                    caller_id: server_info.server_id.clone(),
                    caller_type: audit::CallerType::ManagementServer,
                    source_ip,
                    certificate_fingerprint: Some(cert_info.fingerprint.clone()),
                },
                api_path: request_path.to_string(),
                action: "api_permission_check".to_string(),
                result: audit::SecurityEventResult::Failure("API权限不足".to_string()),
                details: serde_json::json!({
                    "allowed_apis": server_info.allowed_apis,
                    "requested_api": request_path
                }),
            };
            self.auditor.record_event(event).await?;
            
            return Err(SecurityError::InsufficientPermissions);
        }
        
        // 5. 应用访问控制策略
        self.access_control.check_api_permission(
            request_path,
            &access_control::HttpMethod::from_path(request_path),
            &access_control::CallerType::ManagementServer,
        )?;
        
        // 6. 应用速率限制
        self.rate_limiter.check_rate_limit(&server_info.server_id, request_path).await?;
        
        // 记录成功的验证事件
        let event = audit::SecurityEvent {
            event_id: uuid::Uuid::new_v4().to_string(),
            event_type: audit::SecurityEventType::CertificateVerification,
            timestamp: Utc::now(),
            caller_info: audit::CallerInfo {
                caller_id: server_info.server_id.clone(),
                caller_type: audit::CallerType::ManagementServer,
                source_ip,
                certificate_fingerprint: Some(cert_info.fingerprint.clone()),
            },
            api_path: request_path.to_string(),
            action: "management_call_verification".to_string(),
            result: audit::SecurityEventResult::Success,
            details: serde_json::json!({
                "server_name": server_info.server_name
            }),
        };
        self.auditor.record_event(event).await?;
        
        Ok(())
    }

    /// 验证节点间调用
    pub async fn verify_node_call(
        &self,
        client_cert: &certificate::Certificate,
        source_node_id: &str,
        request_path: &str,
        source_ip: IpAddr,
    ) -> SecurityResult<()> {
        // 1. 验证客户端证书
        let cert_info = self.cert_manager.verify_certificate(client_cert).await?;
        
        // 2. 检查节点是否在信任列表中
        let node_info = self.find_trusted_node(source_node_id)?;
        
        // 3. 验证证书指纹
        if node_info.certificate_fingerprint != cert_info.fingerprint {
            let event = audit::SecurityEvent {
                event_id: uuid::Uuid::new_v4().to_string(),
                event_type: audit::SecurityEventType::CertificateVerification,
                timestamp: Utc::now(),
                caller_info: audit::CallerInfo {
                    caller_id: source_node_id.to_string(),
                    caller_type: audit::CallerType::ForwarderNode,
                    source_ip,
                    certificate_fingerprint: Some(cert_info.fingerprint.clone()),
                },
                api_path: request_path.to_string(),
                action: "certificate_fingerprint_verification".to_string(),
                result: audit::SecurityEventResult::Failure("证书指纹不匹配".to_string()),
                details: serde_json::json!({
                    "expected_fingerprint": node_info.certificate_fingerprint,
                    "actual_fingerprint": cert_info.fingerprint
                }),
            };
            self.auditor.record_event(event).await?;
            
            return Err(SecurityError::CertificateVerificationFailed {
                reason: "证书指纹不匹配".to_string(),
            });
        }
        
        // 4. 检查节点是否活跃
        if !node_info.is_active {
            let event = audit::SecurityEvent {
                event_id: uuid::Uuid::new_v4().to_string(),
                event_type: audit::SecurityEventType::AccessDenied,
                timestamp: Utc::now(),
                caller_info: audit::CallerInfo {
                    caller_id: source_node_id.to_string(),
                    caller_type: audit::CallerType::ForwarderNode,
                    source_ip,
                    certificate_fingerprint: Some(cert_info.fingerprint.clone()),
                },
                api_path: request_path.to_string(),
                action: "node_activity_check".to_string(),
                result: audit::SecurityEventResult::Failure("节点不活跃".to_string()),
                details: serde_json::json!({
                    "node_name": node_info.node_name,
                    "is_active": node_info.is_active,
                    "last_seen": node_info.last_seen
                }),
            };
            self.auditor.record_event(event).await?;
            
            return Err(SecurityError::NodeInactive);
        }
        
        // 5. 检查API路径权限
        if !self.access_control.is_inter_node_api(request_path) {
            let event = audit::SecurityEvent {
                event_id: uuid::Uuid::new_v4().to_string(),
                event_type: audit::SecurityEventType::UnauthorizedApiCall,
                timestamp: Utc::now(),
                caller_info: audit::CallerInfo {
                    caller_id: source_node_id.to_string(),
                    caller_type: audit::CallerType::ForwarderNode,
                    source_ip,
                    certificate_fingerprint: Some(cert_info.fingerprint.clone()),
                },
                api_path: request_path.to_string(),
                action: "inter_node_api_check".to_string(),
                result: audit::SecurityEventResult::Failure("非节点间API".to_string()),
                details: serde_json::json!({
                    "requested_api": request_path
                }),
            };
            self.auditor.record_event(event).await?;
            
            return Err(SecurityError::UnauthorizedEndpoint);
        }
        
        // 6. 应用速率限制
        self.rate_limiter.check_rate_limit(source_node_id, request_path).await?;
        
        // 更新节点最后活跃时间
        if let Some(mut node) = self.trusted_nodes.get_mut(source_node_id) {
            node.last_seen = Utc::now();
        }
        
        // 记录成功的验证事件
        let event = audit::SecurityEvent {
            event_id: uuid::Uuid::new_v4().to_string(),
            event_type: audit::SecurityEventType::CertificateVerification,
            timestamp: Utc::now(),
            caller_info: audit::CallerInfo {
                caller_id: source_node_id.to_string(),
                caller_type: audit::CallerType::ForwarderNode,
                source_ip,
                certificate_fingerprint: Some(cert_info.fingerprint.clone()),
            },
            api_path: request_path.to_string(),
            action: "inter_node_call_verification".to_string(),
            result: audit::SecurityEventResult::Success,
            details: serde_json::json!({
                "node_name": node_info.node_name,
                "region": node_info.region
            }),
        };
        self.auditor.record_event(event).await?;
        
        Ok(())
    }

    /// 更新信任节点列表
    pub async fn update_trusted_nodes(&self, nodes: Vec<TrustedNode>) -> SecurityResult<()> {
        self.trusted_nodes.clear();
        for node in nodes {
            self.trusted_nodes.insert(node.node_id.clone(), node);
        }
        
        tracing::info!("更新了信任节点列表，共 {} 个节点", self.trusted_nodes.len());
        Ok(())
    }

    /// 查找信任的服务器
    fn find_trusted_server(&self, fingerprint: &str) -> SecurityResult<TrustedServer> {
        for server in self.trusted_servers.iter() {
            if server.certificate_fingerprint == fingerprint {
                return Ok(server.clone());
            }
        }
        
        Err(SecurityError::CallerNotTrusted)
    }

    /// 查找信任的节点
    fn find_trusted_node(&self, node_id: &str) -> SecurityResult<TrustedNode> {
        self.trusted_nodes
            .get(node_id)
            .map(|node| node.clone())
            .ok_or(SecurityError::CallerNotTrusted)
    }

    /// 匹配API模式
    fn match_api_pattern(&self, pattern: &str, path: &str) -> bool {
        // 简单的通配符匹配实现
        if pattern.ends_with("*") {
            let prefix = &pattern[..pattern.len() - 1];
            path.starts_with(prefix)
        } else {
            pattern == path
        }
    }

    /// 获取安全统计信息
    pub async fn get_security_metrics(&self) -> SecurityResult<crate::types::SecurityMetrics> {
        self.auditor.get_security_metrics().await
    }

    /// 获取证书管理器
    pub fn certificate_manager(&self) -> Arc<certificate::CertificateManager> {
        self.cert_manager.clone()
    }

    /// 获取审计器
    pub fn auditor(&self) -> Arc<audit::SecurityAuditor> {
        self.auditor.clone()
    }
}

/// 信任的节点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustedNode {
    /// 节点ID
    pub node_id: String,
    /// 节点名称
    pub node_name: String,
    /// 所属区域
    pub region: String,
    /// 证书指纹
    pub certificate_fingerprint: String,
    /// 是否活跃
    pub is_active: bool,
    /// 最后活跃时间
    pub last_seen: DateTime<Utc>,
}

/// 信任的服务器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustedServer {
    /// 服务器ID
    pub server_id: String,
    /// 服务器名称
    pub server_name: String,
    /// 证书指纹
    pub certificate_fingerprint: String,
    /// 允许的API列表
    pub allowed_apis: Vec<String>,
    /// 是否活跃
    pub is_active: bool,
    /// 最后活跃时间
    pub last_seen: DateTime<Utc>,
}


