// 安全审计模块
// 负责安全事件的记录、存储和分析

use std::sync::Arc;
use std::net::IpAddr;
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use tracing::{info, warn, error};

use crate::error::{AuditError, AuditResult};
use crate::types::SecurityMetrics;

/// 安全审计器
pub struct SecurityAuditor {
    /// 事件存储
    event_store: Arc<dyn EventStore>,
    /// 告警管理器
    alert_manager: AlertManager,
    /// 安全指标
    metrics: Arc<RwLock<SecurityMetrics>>,
    /// 事件缓存
    event_cache: Arc<RwLock<Vec<SecurityEvent>>>,
}

impl SecurityAuditor {
    /// 创建新的安全审计器
    pub async fn new() -> AuditResult<Self> {
        let event_store = Arc::new(MemoryEventStore::new());
        let alert_manager = AlertManager::new();
        let metrics = Arc::new(RwLock::new(SecurityMetrics::default()));
        let event_cache = Arc::new(RwLock::new(Vec::new()));

        Ok(Self {
            event_store,
            alert_manager,
            metrics,
            event_cache,
        })
    }

    /// 记录安全事件
    pub async fn record_event(&self, event: SecurityEvent) -> AuditResult<()> {
        // 存储事件
        self.event_store.store_event(&event).await?;

        // 更新指标
        self.update_metrics(&event).await;

        // 缓存事件
        let mut cache = self.event_cache.write().await;
        cache.push(event.clone());
        
        // 限制缓存大小
        if cache.len() > 1000 {
            cache.drain(0..500); // 保留最近的500个事件
        }

        // 检查是否需要告警
        if self.should_alert(&event) {
            self.alert_manager.send_security_alert(&event).await?;
        }

        // 记录结构化日志
        match &event.result {
            SecurityEventResult::Success => {
                info!(
                    event_type = ?event.event_type,
                    caller_id = %event.caller_info.caller_id,
                    api_path = %event.api_path,
                    action = %event.action,
                    "安全事件记录成功"
                );
            }
            SecurityEventResult::Failure(reason) => {
                warn!(
                    event_type = ?event.event_type,
                    caller_id = %event.caller_info.caller_id,
                    api_path = %event.api_path,
                    action = %event.action,
                    reason = %reason,
                    "安全事件记录失败"
                );
            }
            SecurityEventResult::Warning(warning) => {
                warn!(
                    event_type = ?event.event_type,
                    caller_id = %event.caller_info.caller_id,
                    api_path = %event.api_path,
                    action = %event.action,
                    warning = %warning,
                    "安全事件记录警告"
                );
            }
        }

        Ok(())
    }

    /// 更新安全指标
    async fn update_metrics(&self, event: &SecurityEvent) {
        let mut metrics = self.metrics.write().await;

        match event.event_type {
            SecurityEventType::CertificateVerification => {
                metrics.certificate_verifications += 1;
                if matches!(event.result, SecurityEventResult::Failure(_)) {
                    metrics.certificate_verification_failures += 1;
                }
            }
            SecurityEventType::AccessDenied => {
                match event.caller_info.caller_type {
                    CallerType::ManagementServer => {
                        metrics.management_server_calls += 1;
                        metrics.management_server_call_failures += 1;
                    }
                    CallerType::ForwarderNode => {
                        metrics.inter_node_calls += 1;
                        metrics.inter_node_call_failures += 1;
                    }
                }
            }
            SecurityEventType::RateLimitExceeded => {
                metrics.rate_limited_requests += 1;
            }
            SecurityEventType::UnauthorizedApiCall => {
                metrics.untrusted_callers += 1;
            }
            _ => {}
        }
    }

    /// 判断是否需要告警
    fn should_alert(&self, event: &SecurityEvent) -> bool {
        matches!(
            event.event_type,
            SecurityEventType::AccessDenied
                | SecurityEventType::SuspiciousActivity
                | SecurityEventType::UnauthorizedApiCall
                | SecurityEventType::CertificateExpired
        ) && matches!(event.result, SecurityEventResult::Failure(_))
    }

    /// 获取安全指标
    pub async fn get_security_metrics(&self) -> crate::error::SecurityResult<SecurityMetrics> {
        Ok(self.metrics.read().await.clone())
    }

    /// 查询安全事件
    pub async fn query_events(
        &self,
        filter: EventFilter,
    ) -> AuditResult<Vec<SecurityEvent>> {
        self.event_store.query_events(filter).await
    }

    /// 获取最近的安全事件
    pub async fn get_recent_events(&self, limit: usize) -> Vec<SecurityEvent> {
        let cache = self.event_cache.read().await;
        cache.iter().rev().take(limit).cloned().collect()
    }

    /// 生成安全报告
    pub async fn generate_security_report(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> AuditResult<SecurityReport> {
        let filter = EventFilter {
            start_time: Some(start_time),
            end_time: Some(end_time),
            event_types: None,
            caller_types: None,
            result_types: None,
        };

        let events = self.query_events(filter).await?;
        let metrics = self.get_security_metrics().await.unwrap_or_default();

        Ok(SecurityReport {
            report_id: uuid::Uuid::new_v4().to_string(),
            generated_at: Utc::now(),
            period_start: start_time,
            period_end: end_time,
            total_events: events.len(),
            events_by_type: Self::group_events_by_type(&events),
            events_by_result: Self::group_events_by_result(&events),
            security_metrics: metrics,
            top_callers: Self::get_top_callers(&events, 10),
            suspicious_activities: Self::identify_suspicious_activities(&events),
        })
    }

    /// 按类型分组事件
    fn group_events_by_type(events: &[SecurityEvent]) -> HashMap<SecurityEventType, usize> {
        let mut groups = HashMap::new();
        for event in events {
            *groups.entry(event.event_type.clone()).or_insert(0) += 1;
        }
        groups
    }

    /// 按结果分组事件
    fn group_events_by_result(events: &[SecurityEvent]) -> HashMap<String, usize> {
        let mut groups = HashMap::new();
        for event in events {
            let result_type = match &event.result {
                SecurityEventResult::Success => "成功",
                SecurityEventResult::Failure(_) => "失败",
                SecurityEventResult::Warning(_) => "警告",
            };
            *groups.entry(result_type.to_string()).or_insert(0) += 1;
        }
        groups
    }

    /// 获取顶级调用者
    fn get_top_callers(events: &[SecurityEvent], limit: usize) -> Vec<CallerSummary> {
        let mut caller_counts = HashMap::new();
        
        for event in events {
            let entry = caller_counts
                .entry(event.caller_info.caller_id.clone())
                .or_insert(CallerSummary {
                    caller_id: event.caller_info.caller_id.clone(),
                    caller_type: event.caller_info.caller_type.clone(),
                    total_calls: 0,
                    failed_calls: 0,
                    last_seen: event.timestamp,
                });
            
            entry.total_calls += 1;
            if matches!(event.result, SecurityEventResult::Failure(_)) {
                entry.failed_calls += 1;
            }
            if event.timestamp > entry.last_seen {
                entry.last_seen = event.timestamp;
            }
        }

        let mut callers: Vec<_> = caller_counts.into_values().collect();
        callers.sort_by(|a, b| b.total_calls.cmp(&a.total_calls));
        callers.truncate(limit);
        callers
    }

    /// 识别可疑活动
    fn identify_suspicious_activities(events: &[SecurityEvent]) -> Vec<SuspiciousActivity> {
        let mut activities = Vec::new();

        // 检测频繁失败的调用者
        let mut failure_counts = HashMap::new();
        for event in events {
            if matches!(event.result, SecurityEventResult::Failure(_)) {
                *failure_counts
                    .entry(event.caller_info.caller_id.clone())
                    .or_insert(0) += 1;
            }
        }

        for (caller_id, count) in failure_counts {
            if count > 10 {
                activities.push(SuspiciousActivity {
                    activity_type: "频繁失败".to_string(),
                    description: format!("调用者 {} 在时间段内失败 {} 次", caller_id, count),
                    severity: if count > 50 { "高" } else { "中" }.to_string(),
                    caller_id: Some(caller_id),
                    first_seen: events.first().map(|e| e.timestamp).unwrap_or_else(Utc::now),
                    last_seen: events.last().map(|e| e.timestamp).unwrap_or_else(Utc::now),
                });
            }
        }

        activities
    }
}

/// 安全事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityEvent {
    /// 事件ID
    pub event_id: String,
    /// 事件类型
    pub event_type: SecurityEventType,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 调用者信息
    pub caller_info: CallerInfo,
    /// API路径
    pub api_path: String,
    /// 操作
    pub action: String,
    /// 结果
    pub result: SecurityEventResult,
    /// 详细信息
    pub details: serde_json::Value,
}

/// 安全事件类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SecurityEventType {
    /// 证书验证
    CertificateVerification,
    /// 访问被拒绝
    AccessDenied,
    /// 速率限制超出
    RateLimitExceeded,
    /// 可疑活动
    SuspiciousActivity,
    /// 证书过期
    CertificateExpired,
    /// 未授权的API调用
    UnauthorizedApiCall,
}

/// 调用者信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CallerInfo {
    /// 调用者ID
    pub caller_id: String,
    /// 调用者类型
    pub caller_type: CallerType,
    /// 源IP地址
    pub source_ip: IpAddr,
    /// 证书指纹
    pub certificate_fingerprint: Option<String>,
}

/// 调用者类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CallerType {
    /// 管理服务器
    ManagementServer,
    /// 转发节点
    ForwarderNode,
}

/// 安全事件结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityEventResult {
    /// 成功
    Success,
    /// 失败
    Failure(String),
    /// 警告
    Warning(String),
}

/// 事件过滤器
#[derive(Debug, Clone)]
pub struct EventFilter {
    /// 开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 事件类型
    pub event_types: Option<Vec<SecurityEventType>>,
    /// 调用者类型
    pub caller_types: Option<Vec<CallerType>>,
    /// 结果类型
    pub result_types: Option<Vec<String>>,
}

/// 事件存储接口
#[async_trait::async_trait]
pub trait EventStore: Send + Sync {
    /// 存储事件
    async fn store_event(&self, event: &SecurityEvent) -> AuditResult<()>;
    
    /// 查询事件
    async fn query_events(&self, filter: EventFilter) -> AuditResult<Vec<SecurityEvent>>;
    
    /// 删除过期事件
    async fn cleanup_old_events(&self, retention_days: u32) -> AuditResult<()>;
}

/// 内存事件存储
pub struct MemoryEventStore {
    events: Arc<RwLock<Vec<SecurityEvent>>>,
}

impl MemoryEventStore {
    pub fn new() -> Self {
        Self {
            events: Arc::new(RwLock::new(Vec::new())),
        }
    }
}

#[async_trait::async_trait]
impl EventStore for MemoryEventStore {
    async fn store_event(&self, event: &SecurityEvent) -> AuditResult<()> {
        let mut events = self.events.write().await;
        events.push(event.clone());
        
        // 限制内存中的事件数量
        if events.len() > 10000 {
            events.drain(0..5000); // 保留最近的5000个事件
        }
        
        Ok(())
    }

    async fn query_events(&self, filter: EventFilter) -> AuditResult<Vec<SecurityEvent>> {
        let events = self.events.read().await;
        let mut filtered_events = Vec::new();

        for event in events.iter() {
            // 时间过滤
            if let Some(start) = filter.start_time {
                if event.timestamp < start {
                    continue;
                }
            }
            if let Some(end) = filter.end_time {
                if event.timestamp > end {
                    continue;
                }
            }

            // 事件类型过滤
            if let Some(ref types) = filter.event_types {
                if !types.contains(&event.event_type) {
                    continue;
                }
            }

            // 调用者类型过滤
            if let Some(ref caller_types) = filter.caller_types {
                if !caller_types.contains(&event.caller_info.caller_type) {
                    continue;
                }
            }

            filtered_events.push(event.clone());
        }

        Ok(filtered_events)
    }

    async fn cleanup_old_events(&self, retention_days: u32) -> AuditResult<()> {
        let cutoff_time = Utc::now() - chrono::Duration::days(retention_days as i64);
        let mut events = self.events.write().await;
        
        let original_count = events.len();
        events.retain(|event| event.timestamp > cutoff_time);
        let removed_count = original_count - events.len();
        
        if removed_count > 0 {
            info!("清理了 {} 个过期的安全事件", removed_count);
        }
        
        Ok(())
    }
}

/// 告警管理器
pub struct AlertManager {
    // 简化实现，实际应该包含告警配置和发送逻辑
}

impl AlertManager {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn send_security_alert(&self, event: &SecurityEvent) -> AuditResult<()> {
        // 简化实现，实际应该发送告警到外部系统
        warn!(
            event_id = %event.event_id,
            event_type = ?event.event_type,
            caller_id = %event.caller_info.caller_id,
            "发送安全告警"
        );
        Ok(())
    }
}

/// 安全报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityReport {
    /// 报告ID
    pub report_id: String,
    /// 生成时间
    pub generated_at: DateTime<Utc>,
    /// 统计期间开始时间
    pub period_start: DateTime<Utc>,
    /// 统计期间结束时间
    pub period_end: DateTime<Utc>,
    /// 总事件数
    pub total_events: usize,
    /// 按类型分组的事件
    pub events_by_type: HashMap<SecurityEventType, usize>,
    /// 按结果分组的事件
    pub events_by_result: HashMap<String, usize>,
    /// 安全指标
    pub security_metrics: SecurityMetrics,
    /// 顶级调用者
    pub top_callers: Vec<CallerSummary>,
    /// 可疑活动
    pub suspicious_activities: Vec<SuspiciousActivity>,
}

/// 调用者摘要
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CallerSummary {
    /// 调用者ID
    pub caller_id: String,
    /// 调用者类型
    pub caller_type: CallerType,
    /// 总调用次数
    pub total_calls: usize,
    /// 失败调用次数
    pub failed_calls: usize,
    /// 最后活跃时间
    pub last_seen: DateTime<Utc>,
}

/// 可疑活动
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuspiciousActivity {
    /// 活动类型
    pub activity_type: String,
    /// 描述
    pub description: String,
    /// 严重程度
    pub severity: String,
    /// 相关调用者ID
    pub caller_id: Option<String>,
    /// 首次发现时间
    pub first_seen: DateTime<Utc>,
    /// 最后发现时间
    pub last_seen: DateTime<Utc>,
}
