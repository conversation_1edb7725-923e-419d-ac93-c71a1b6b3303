// 速率限制模块
// 实现基于令牌桶算法的速率限制功能

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{debug, warn};

use crate::config::RateLimitConfig;
use crate::error::{SecurityError, SecurityResult};

/// 速率限制器
pub struct RateLimiter {
    /// 配置
    config: RateLimitConfig,
    /// 令牌桶映射
    buckets: Arc<RwLock<HashMap<String, TokenBucket>>>,
    /// 清理任务句柄
    cleanup_task: Option<tokio::task::Jo<PERSON><PERSON><PERSON><PERSON><()>>,
}

impl RateLimiter {
    /// 创建新的速率限制器
    pub fn new(config: RateLimitConfig) -> Self {
        let buckets = Arc::new(RwLock::new(HashMap::new()));
        
        let mut limiter = Self {
            config,
            buckets,
            cleanup_task: None,
        };

        // 启动清理任务
        if limiter.config.enabled {
            limiter.start_cleanup_task();
        }

        limiter
    }

    /// 启动清理任务
    fn start_cleanup_task(&mut self) {
        let buckets = self.buckets.clone();
        let cleanup_interval = Duration::from_secs(60); // 每分钟清理一次

        let task = tokio::spawn(async move {
            let mut interval = interval(cleanup_interval);
            
            loop {
                interval.tick().await;
                Self::cleanup_expired_buckets(&buckets).await;
            }
        });

        self.cleanup_task = Some(task);
    }

    /// 清理过期的令牌桶
    async fn cleanup_expired_buckets(buckets: &Arc<RwLock<HashMap<String, TokenBucket>>>) {
        let mut buckets_guard = buckets.write().await;
        let now = Instant::now();
        
        let initial_count = buckets_guard.len();
        buckets_guard.retain(|_, bucket| {
            // 保留最近5分钟内有活动的桶
            now.duration_since(bucket.last_access) < Duration::from_secs(300)
        });
        
        let removed_count = initial_count - buckets_guard.len();
        if removed_count > 0 {
            debug!("清理了 {} 个过期的速率限制桶", removed_count);
        }
    }

    /// 检查速率限制
    pub async fn check_rate_limit(&self, caller_id: &str, api_path: &str) -> SecurityResult<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let bucket_key = format!("{}:{}", caller_id, api_path);
        let mut buckets = self.buckets.write().await;
        
        let bucket = buckets.entry(bucket_key.clone()).or_insert_with(|| {
            TokenBucket::new(
                self.config.max_requests_per_minute,
                self.config.burst_size,
                Duration::from_secs(self.config.window_size_seconds),
            )
        });

        if bucket.try_consume() {
            Ok(())
        } else {
            warn!(
                caller_id = %caller_id,
                api_path = %api_path,
                "速率限制触发"
            );
            Err(SecurityError::RateLimitExceeded)
        }
    }

    /// 获取速率限制状态
    pub async fn get_rate_limit_status(&self, caller_id: &str, api_path: &str) -> RateLimitStatus {
        if !self.config.enabled {
            return RateLimitStatus {
                enabled: false,
                remaining_tokens: u32::MAX,
                reset_time: None,
                total_requests: 0,
                window_start: Instant::now(),
            };
        }

        let bucket_key = format!("{}:{}", caller_id, api_path);
        let buckets = self.buckets.read().await;
        
        if let Some(bucket) = buckets.get(&bucket_key) {
            RateLimitStatus {
                enabled: true,
                remaining_tokens: bucket.available_tokens(),
                reset_time: Some(bucket.next_refill_time()),
                total_requests: bucket.total_requests(),
                window_start: bucket.window_start(),
            }
        } else {
            RateLimitStatus {
                enabled: true,
                remaining_tokens: self.config.max_requests_per_minute,
                reset_time: None,
                total_requests: 0,
                window_start: Instant::now(),
            }
        }
    }

    /// 重置速率限制
    pub async fn reset_rate_limit(&self, caller_id: &str, api_path: &str) -> SecurityResult<()> {
        let bucket_key = format!("{}:{}", caller_id, api_path);
        let mut buckets = self.buckets.write().await;
        
        if buckets.remove(&bucket_key).is_some() {
            debug!(
                caller_id = %caller_id,
                api_path = %api_path,
                "重置速率限制"
            );
        }
        
        Ok(())
    }

    /// 获取所有活跃的速率限制状态
    pub async fn get_all_rate_limits(&self) -> HashMap<String, RateLimitStatus> {
        let mut result = HashMap::new();
        let buckets = self.buckets.read().await;
        
        for (key, bucket) in buckets.iter() {
            result.insert(key.clone(), RateLimitStatus {
                enabled: self.config.enabled,
                remaining_tokens: bucket.available_tokens(),
                reset_time: Some(bucket.next_refill_time()),
                total_requests: bucket.total_requests(),
                window_start: bucket.window_start(),
            });
        }
        
        result
    }

    /// 更新配置
    pub async fn update_config(&mut self, new_config: RateLimitConfig) {
        let was_enabled = self.config.enabled;
        self.config = new_config;

        // 如果启用状态发生变化，处理清理任务
        if !was_enabled && self.config.enabled {
            self.start_cleanup_task();
        } else if was_enabled && !self.config.enabled {
            if let Some(task) = self.cleanup_task.take() {
                task.abort();
            }
        }

        // 清空现有的桶，使用新配置
        if self.config.enabled {
            let mut buckets = self.buckets.write().await;
            buckets.clear();
        }
    }
}

impl Drop for RateLimiter {
    fn drop(&mut self) {
        if let Some(task) = self.cleanup_task.take() {
            task.abort();
        }
    }
}

/// 令牌桶
#[derive(Debug)]
struct TokenBucket {
    /// 最大令牌数
    max_tokens: u32,
    /// 当前令牌数
    current_tokens: u32,
    /// 突发大小
    burst_size: u32,
    /// 窗口大小
    window_size: Duration,
    /// 窗口开始时间
    window_start: Instant,
    /// 最后访问时间
    last_access: Instant,
    /// 总请求数
    total_requests: u32,
}

impl TokenBucket {
    /// 创建新的令牌桶
    fn new(max_tokens: u32, burst_size: u32, window_size: Duration) -> Self {
        let now = Instant::now();
        Self {
            max_tokens,
            current_tokens: max_tokens,
            burst_size,
            window_size,
            window_start: now,
            last_access: now,
            total_requests: 0,
        }
    }

    /// 尝试消费一个令牌
    fn try_consume(&mut self) -> bool {
        let now = Instant::now();
        self.last_access = now;
        self.total_requests += 1;

        // 检查是否需要重置窗口
        if now.duration_since(self.window_start) >= self.window_size {
            self.reset_window(now);
        }

        // 检查是否有可用令牌
        if self.current_tokens > 0 {
            self.current_tokens -= 1;
            true
        } else {
            // 检查是否可以使用突发容量
            if self.total_requests <= self.burst_size {
                true
            } else {
                false
            }
        }
    }

    /// 重置窗口
    fn reset_window(&mut self, now: Instant) {
        self.window_start = now;
        self.current_tokens = self.max_tokens;
        self.total_requests = 0;
    }

    /// 获取可用令牌数
    fn available_tokens(&self) -> u32 {
        let now = Instant::now();
        
        // 如果窗口已过期，返回最大令牌数
        if now.duration_since(self.window_start) >= self.window_size {
            self.max_tokens
        } else {
            self.current_tokens
        }
    }

    /// 获取下次补充时间
    fn next_refill_time(&self) -> Instant {
        self.window_start + self.window_size
    }

    /// 获取总请求数
    fn total_requests(&self) -> u32 {
        self.total_requests
    }

    /// 获取窗口开始时间
    fn window_start(&self) -> Instant {
        self.window_start
    }
}

/// 速率限制状态
#[derive(Debug, Clone)]
pub struct RateLimitStatus {
    /// 是否启用
    pub enabled: bool,
    /// 剩余令牌数
    pub remaining_tokens: u32,
    /// 重置时间
    pub reset_time: Option<Instant>,
    /// 总请求数
    pub total_requests: u32,
    /// 窗口开始时间
    pub window_start: Instant,
}

/// 速率限制统计
#[derive(Debug, Clone)]
pub struct RateLimitStats {
    /// 总的速率限制检查次数
    pub total_checks: u64,
    /// 被限制的请求数
    pub limited_requests: u64,
    /// 活跃的桶数量
    pub active_buckets: usize,
    /// 平均令牌消耗率
    pub average_consumption_rate: f64,
}

impl RateLimiter {
    /// 获取速率限制统计
    pub async fn get_stats(&self) -> RateLimitStats {
        let buckets = self.buckets.read().await;
        
        let mut total_requests = 0u64;
        let mut limited_requests = 0u64;
        
        for bucket in buckets.values() {
            total_requests += bucket.total_requests as u64;
            // 简化统计，实际应该跟踪被限制的请求
        }
        
        RateLimitStats {
            total_checks: total_requests,
            limited_requests,
            active_buckets: buckets.len(),
            average_consumption_rate: if total_requests > 0 {
                total_requests as f64 / buckets.len() as f64
            } else {
                0.0
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    #[tokio::test]
    async fn test_rate_limiter_basic() {
        let config = RateLimitConfig {
            enabled: true,
            max_requests_per_minute: 5,
            burst_size: 2,
            window_size_seconds: 60,
        };

        let limiter = RateLimiter::new(config);
        let caller_id = "test_caller";
        let api_path = "/api/test";

        // 前5个请求应该成功
        for i in 0..5 {
            let result = limiter.check_rate_limit(caller_id, api_path).await;
            assert!(result.is_ok(), "请求 {} 应该成功", i + 1);
        }

        // 第6个请求应该失败（超过限制）
        let result = limiter.check_rate_limit(caller_id, api_path).await;
        assert!(result.is_err(), "第6个请求应该被限制");
    }

    #[tokio::test]
    async fn test_rate_limiter_burst() {
        let config = RateLimitConfig {
            enabled: true,
            max_requests_per_minute: 2,
            burst_size: 5,
            window_size_seconds: 60,
        };

        let limiter = RateLimiter::new(config);
        let caller_id = "test_caller";
        let api_path = "/api/test";

        // 突发请求应该在突发限制内成功
        for i in 0..5 {
            let result = limiter.check_rate_limit(caller_id, api_path).await;
            assert!(result.is_ok(), "突发请求 {} 应该成功", i + 1);
        }

        // 超过突发限制的请求应该失败
        let result = limiter.check_rate_limit(caller_id, api_path).await;
        assert!(result.is_err(), "超过突发限制的请求应该被限制");
    }

    #[tokio::test]
    async fn test_rate_limiter_disabled() {
        let config = RateLimitConfig {
            enabled: false,
            max_requests_per_minute: 1,
            burst_size: 1,
            window_size_seconds: 60,
        };

        let limiter = RateLimiter::new(config);
        let caller_id = "test_caller";
        let api_path = "/api/test";

        // 当禁用时，所有请求都应该成功
        for _ in 0..100 {
            let result = limiter.check_rate_limit(caller_id, api_path).await;
            assert!(result.is_ok(), "禁用时所有请求都应该成功");
        }
    }

    #[test]
    fn test_token_bucket() {
        let mut bucket = TokenBucket::new(5, 2, Duration::from_secs(60));

        // 消费5个令牌
        for _ in 0..5 {
            assert!(bucket.try_consume(), "应该能够消费令牌");
        }

        // 第6个令牌应该失败
        assert!(!bucket.try_consume(), "应该无法消费更多令牌");

        // 检查可用令牌数
        assert_eq!(bucket.available_tokens(), 0);
    }
}
