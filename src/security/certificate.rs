// 证书管理模块
// 负责证书的加载、验证、轮换和管理

use std::path::Path;
use std::sync::Arc;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use tokio::fs;
use tracing::{info, warn, error};

use crate::error::{SecurityError, SecurityResult};

/// 证书管理器
pub struct CertificateManager {
    /// 节点证书
    node_certificate: Arc<Certificate>,
    /// 节点私钥
    node_private_key: Arc<PrivateKey>,
    /// CA证书
    ca_certificate: Arc<Certificate>,
    /// 证书文件路径
    cert_path: String,
    /// 私钥文件路径
    key_path: String,
    /// CA证书文件路径
    ca_path: String,
}

impl CertificateManager {
    /// 创建新的证书管理器
    pub async fn new(
        cert_path: &str,
        key_path: &str,
        ca_path: &str,
    ) -> SecurityResult<Self> {
        // 加载节点证书
        let node_certificate = Arc::new(Self::load_certificate(cert_path).await?);
        
        // 加载节点私钥
        let node_private_key = Arc::new(Self::load_private_key(key_path).await?);
        
        // 加载CA证书
        let ca_certificate = Arc::new(Self::load_certificate(ca_path).await?);
        
        // 验证证书链
        Self::verify_certificate_chain(&node_certificate, &ca_certificate)?;
        
        info!("证书管理器初始化完成");
        info!("节点证书主题: {}", node_certificate.subject);
        info!("节点证书有效期: {} - {}", node_certificate.not_before, node_certificate.not_after);
        info!("CA证书主题: {}", ca_certificate.subject);
        
        Ok(Self {
            node_certificate,
            node_private_key,
            ca_certificate,
            cert_path: cert_path.to_string(),
            key_path: key_path.to_string(),
            ca_path: ca_path.to_string(),
        })
    }

    /// 加载证书文件
    async fn load_certificate(path: &str) -> SecurityResult<Certificate> {
        if !Path::new(path).exists() {
            return Err(SecurityError::CertificateVerificationFailed {
                reason: format!("证书文件不存在: {}", path),
            });
        }

        let pem_data = fs::read_to_string(path).await.map_err(|e| {
            SecurityError::CertificateVerificationFailed {
                reason: format!("读取证书文件失败: {}", e),
            }
        })?;

        Self::parse_certificate(&pem_data)
    }

    /// 加载私钥文件
    async fn load_private_key(path: &str) -> SecurityResult<PrivateKey> {
        if !Path::new(path).exists() {
            return Err(SecurityError::CertificateVerificationFailed {
                reason: format!("私钥文件不存在: {}", path),
            });
        }

        let pem_data = fs::read_to_string(path).await.map_err(|e| {
            SecurityError::CertificateVerificationFailed {
                reason: format!("读取私钥文件失败: {}", e),
            }
        })?;

        Self::parse_private_key(&pem_data)
    }

    /// 解析证书
    fn parse_certificate(pem_data: &str) -> SecurityResult<Certificate> {
        // 简化的证书解析实现
        // 在实际实现中，应该使用rustls或openssl库来解析证书
        
        // 提取证书信息（这里是模拟实现）
        let subject = Self::extract_subject(pem_data)?;
        let issuer = Self::extract_issuer(pem_data)?;
        let (not_before, not_after) = Self::extract_validity_period(pem_data)?;
        let fingerprint = Self::calculate_fingerprint(pem_data)?;

        Ok(Certificate {
            pem_data: pem_data.to_string(),
            fingerprint,
            subject,
            issuer,
            not_before,
            not_after,
        })
    }

    /// 解析私钥
    fn parse_private_key(pem_data: &str) -> SecurityResult<PrivateKey> {
        // 简化的私钥解析实现
        // 在实际实现中，应该使用rustls或openssl库来解析私钥
        
        let key_type = if pem_data.contains("RSA PRIVATE KEY") {
            KeyType::Rsa
        } else if pem_data.contains("EC PRIVATE KEY") {
            KeyType::Ec
        } else {
            KeyType::Ed25519
        };

        Ok(PrivateKey {
            pem_data: pem_data.to_string(),
            key_type,
        })
    }

    /// 提取证书主题
    fn extract_subject(pem_data: &str) -> SecurityResult<String> {
        // 简化实现，实际应该解析ASN.1结构
        Ok("CN=forwarder-node".to_string())
    }

    /// 提取证书颁发者
    fn extract_issuer(pem_data: &str) -> SecurityResult<String> {
        // 简化实现，实际应该解析ASN.1结构
        Ok("CN=KVM-Tunnel-CA".to_string())
    }

    /// 提取证书有效期
    fn extract_validity_period(pem_data: &str) -> SecurityResult<(DateTime<Utc>, DateTime<Utc>)> {
        // 简化实现，实际应该解析ASN.1结构
        let now = Utc::now();
        let not_before = now - chrono::Duration::days(30);
        let not_after = now + chrono::Duration::days(365);
        Ok((not_before, not_after))
    }

    /// 计算证书指纹
    fn calculate_fingerprint(pem_data: &str) -> SecurityResult<String> {
        // 简化实现，实际应该计算证书的SHA-256哈希
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        pem_data.hash(&mut hasher);
        let hash = hasher.finish();
        
        Ok(format!("sha256:{:x}", hash))
    }

    /// 验证证书链
    fn verify_certificate_chain(cert: &Certificate, ca_cert: &Certificate) -> SecurityResult<()> {
        // 简化实现，实际应该验证证书签名
        if cert.issuer != ca_cert.subject {
            return Err(SecurityError::CertificateVerificationFailed {
                reason: "证书颁发者与CA主题不匹配".to_string(),
            });
        }
        
        Ok(())
    }

    /// 验证证书
    pub async fn verify_certificate(&self, cert: &Certificate) -> SecurityResult<CertificateInfo> {
        // 1. 验证证书签名
        if !cert.verify_with_ca(&self.ca_certificate) {
            return Err(SecurityError::InvalidCertificate);
        }

        // 2. 检查证书有效期
        if cert.is_expired() {
            return Err(SecurityError::CertificateExpired);
        }

        // 3. 提取证书信息
        Ok(CertificateInfo {
            subject: cert.subject.clone(),
            issuer: cert.issuer.clone(),
            fingerprint: cert.fingerprint.clone(),
            serial_number: "123456789".to_string(), // 简化实现
            valid_until: cert.not_after,
        })
    }

    /// 检查证书是否即将过期
    pub fn check_certificate_expiry(&self, days_threshold: u32) -> Vec<CertificateExpiryWarning> {
        let mut warnings = Vec::new();
        let threshold = Utc::now() + chrono::Duration::days(days_threshold as i64);

        // 检查节点证书
        if self.node_certificate.not_after < threshold {
            warnings.push(CertificateExpiryWarning {
                certificate_type: "节点证书".to_string(),
                subject: self.node_certificate.subject.clone(),
                expires_at: self.node_certificate.not_after,
                days_until_expiry: (self.node_certificate.not_after - Utc::now()).num_days(),
            });
        }

        // 检查CA证书
        if self.ca_certificate.not_after < threshold {
            warnings.push(CertificateExpiryWarning {
                certificate_type: "CA证书".to_string(),
                subject: self.ca_certificate.subject.clone(),
                expires_at: self.ca_certificate.not_after,
                days_until_expiry: (self.ca_certificate.not_after - Utc::now()).num_days(),
            });
        }

        warnings
    }

    /// 重新加载证书
    pub async fn reload_certificates(&mut self) -> SecurityResult<()> {
        info!("重新加载证书");

        // 重新加载节点证书
        let new_node_cert = Self::load_certificate(&self.cert_path).await?;
        
        // 重新加载节点私钥
        let new_node_key = Self::load_private_key(&self.key_path).await?;
        
        // 重新加载CA证书
        let new_ca_cert = Self::load_certificate(&self.ca_path).await?;
        
        // 验证新的证书链
        Self::verify_certificate_chain(&new_node_cert, &new_ca_cert)?;
        
        // 更新证书
        self.node_certificate = Arc::new(new_node_cert);
        self.node_private_key = Arc::new(new_node_key);
        self.ca_certificate = Arc::new(new_ca_cert);
        
        info!("证书重新加载完成");
        Ok(())
    }

    /// 获取节点证书
    pub fn get_node_certificate(&self) -> Arc<Certificate> {
        self.node_certificate.clone()
    }

    /// 获取节点私钥
    pub fn get_node_private_key(&self) -> Arc<PrivateKey> {
        self.node_private_key.clone()
    }

    /// 获取CA证书
    pub fn get_ca_certificate(&self) -> Arc<Certificate> {
        self.ca_certificate.clone()
    }

    /// 获取证书信息
    pub fn get_certificate_info(&self) -> CertificateInfo {
        CertificateInfo {
            subject: self.node_certificate.subject.clone(),
            issuer: self.node_certificate.issuer.clone(),
            fingerprint: self.node_certificate.fingerprint.clone(),
            serial_number: "123456789".to_string(), // 简化实现
            valid_until: self.node_certificate.not_after,
        }
    }
}

/// 证书
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Certificate {
    /// PEM格式的证书数据
    pub pem_data: String,
    /// 证书指纹
    pub fingerprint: String,
    /// 证书主题
    pub subject: String,
    /// 证书颁发者
    pub issuer: String,
    /// 有效期开始时间
    pub not_before: DateTime<Utc>,
    /// 有效期结束时间
    pub not_after: DateTime<Utc>,
}

impl Certificate {
    /// 使用CA证书验证当前证书
    pub fn verify_with_ca(&self, ca_cert: &Certificate) -> bool {
        // 简化实现，实际应该验证证书签名
        self.issuer == ca_cert.subject
    }

    /// 检查证书是否过期
    pub fn is_expired(&self) -> bool {
        Utc::now() > self.not_after
    }

    /// 检查证书指纹是否匹配
    pub fn matches_fingerprint(&self, expected: &str) -> bool {
        self.fingerprint == expected
    }
}

/// 私钥
#[derive(Debug, Clone)]
pub struct PrivateKey {
    /// PEM格式的私钥数据
    pub pem_data: String,
    /// 密钥类型
    pub key_type: KeyType,
}

/// 密钥类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum KeyType {
    /// RSA密钥
    Rsa,
    /// 椭圆曲线密钥
    Ec,
    /// Ed25519密钥
    Ed25519,
}

/// 证书信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateInfo {
    /// 证书主题
    pub subject: String,
    /// 证书颁发者
    pub issuer: String,
    /// 证书指纹
    pub fingerprint: String,
    /// 证书序列号
    pub serial_number: String,
    /// 有效期结束时间
    pub valid_until: DateTime<Utc>,
}

/// 证书过期警告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateExpiryWarning {
    /// 证书类型
    pub certificate_type: String,
    /// 证书主题
    pub subject: String,
    /// 过期时间
    pub expires_at: DateTime<Utc>,
    /// 距离过期天数
    pub days_until_expiry: i64,
}
