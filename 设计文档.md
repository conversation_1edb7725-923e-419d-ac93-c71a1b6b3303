[toc]

# KVM 隧道服务设计文档 (分布式对等架构)

## 1. 引言

### 1.1 项目背景
本项目旨在设计并实现一个基于 mediasoup 和 Rust 的 KVM 隧道服务。该服务允许远程KVM RX设备通过网络安全、高效地访问和控制远端的 KVM TX 设备（或其他目标设备），支持音视频流、输入设备（键盘、鼠标）以及其他数据通道的透传。

### 1.2 设计目标
*   **高性能与低延迟**：利用 mediasoup 实现高效的媒体和数据流处理，Rust 提供内存安全和高并发能力。
*   **可伸缩性**：系统应能通过增加转发节点来水平扩展，以支持更多并发用户和KVM TX设备。
*   **高可用性**：关键组件（如转发管理服务器和转发节点集群）应具备高可用性，减少单点故障。
*   **安全性**：提供端到端的安全保障，包括认证、授权和数据加密。
*   **灵活性**：支持多种类型的数据透传，不仅仅是 KVM。
*   **分布式对等架构**：所有转发节点功能对等，均可独立处理KVM RX设备连接和KVM TX设备连接，节点间可协同完成跨节点路由。

### 1.3 文档范围
本文档详细描述了 KVM 隧道服务的整体架构、模块设计、API 接口、数据结构、核心交互流程、安全机制以及部署运维考虑。

## 2. 系统架构

### 2.1 整体架构图
```mermaid
graph TD
    subgraph "KVM RX设备局域网 (LAN A)"
        C1[KVM RX设备1]
        C2[KVM RX设备2]
        CN[KVM RX设备N]
        KVM_MS_A[KVM管理服务器 A]
        FN_A_Entry["转发节点 A (KVM RX接入)"]
        

    end

    subgraph "KVM TX设备局域网 (LAN B)"
        T1[KVM TX设备1]
        T2[KVM TX设备2]
        TN[KVM TX设备N]
        KVM_MS_B[KVM管理服务器 B]
        FN_B_Target["转发节点 B (KVM TX接入)"]


    end
    
    subgraph "互联网 (WAN)"
        MS["转发管理服务器 (中央)"]
        FN_X[其他转发节点]
        APP[客户端]

    end

    C1 <-->|REST| KVM_MS_A
    C2 <-->|REST| KVM_MS_A
    CN <-->|REST| KVM_MS_A
    MS <-->|REST| FN_A_Entry
    KVM_MS_A <-->|REST| MS
    
    C1 <-->|TCP/IP| FN_A_Entry
    C2 <-->|TCP/IP| FN_A_Entry
    CN <-->|TCP/IP| FN_A_Entry


    MS <-->|REST| FN_B_Target
    KVM_MS_B <-->|REST| MS
    FN_B_Target <-->|TCP/IP, mediasoup| T1
    FN_B_Target <-->|TCP/IP, mediasoup| T2
    FN_B_Target <-->|TCP/IP, mediasoup| TN
    T1 --> KVM_MS_B
    T2 --> KVM_MS_B
    TN --> KVM_MS_B

    FN_A_Entry <-->|REST, mediasoup pipe| FN_B_Target
    FN_A_Entry <-->|REST, mediasoup pipe| FN_X
    FN_B_Target <-->|REST, mediasoup pipe| FN_X

    APP <-->|WebRTC, Websocket|FN_A_Entry
    APP <-->|REST|KVM_MS_A
    
    style KVM_MS_A fill:#cyan,stroke:#333,stroke-width:2px
    style KVM_MS_B fill:#cyan,stroke:#333,stroke-width:2px
    style FN_A_Entry fill:#lightgreen,stroke:#333,stroke-width:2px
    style FN_B_Target fill:#lightgreen,stroke:#333,stroke-width:2px
    style FN_X fill:#lightgreen,stroke:#333,stroke-width:2px
    style MS fill:#lightblue,stroke:#333,stroke-width:2px
    style C1 fill:#orange,stroke:#333,stroke-width:2px
    style C2 fill:#orange,stroke:#333,stroke-width:2px
    style CN fill:#orange,stroke:#333,stroke-width:2px
    style T1 fill:#pink,stroke:#333,stroke-width:2px
    style T2 fill:#pink,stroke:#333,stroke-width:2px
    style TN fill:#pink,stroke:#333,stroke-width:2px
```

### 2.2 核心组件

*   **KVM RX设备**：
    *   KVM系统的RX设备，如云控坐席RX。
    *   首先与其所在局域网的 **KVM管理服务器** 通信，发起对特定KVM TX设备的访问请求。
    *   从KVM管理服务器获取连接参数后，与指定的 **转发节点** 提供的虚拟IP建立连接。
*   **KVM管理服务器 (KVM Local Management Server)**：
    *   部署在每个局域网内部，例如 LAN A 内的 `KVM_MS_A` 和 LAN B 内的 `KVM_MS_B`。
    *   负责管理其局域网内的KVM RX设备和KVM TX设备。
    *   处理来自其局域网内KVM RX设备的访问请求。
    *   向中央的 **转发管理服务器 (MS)** 注册其管理的、可供外部（或其他局域网）访问的KVM TX设备信息。
    *   当收到KVM RX设备的请求时，向中央的 **转发管理服务器 (MS)** 查询目标KVM TX设备的路由信息（例如，哪个转发节点可以接入，以及可能的“虚拟IP”或会话标识）。
    *   将处理后的连接信息（如本地转发节点地址、会话令牌）返回给KVM RX设备。
    *   对于本局域网内的KVM TX设备访问，不涉及 **转发管理服务器 (MS)** 与 **转发节点 (Forwarder Node)**。
*   **转发节点 (Forwarder Node)**：
    *   分布式部署，所有节点功能对等。
    *   **KVM RX接入节点 (如 FN_A_Entry)**：接收来自KVM RX设备的 WebRTC 连接。这些连接是基于KVM RX设备从其本地KVM管理服务器获得的指令。
    *   **KVM TX接入节点 (如 FN_B_Target)**：直接连接和管理KVM TX设备。这些KVM TX设备由其本地的KVM管理服务器管理。
    *   负责与KVM RX设备和KVM TX设备之间的 mediasoup 数据流传输。
    *   根据需要，在不同转发节点之间通过 mediasoup pipe 建立数据通路。
    *   向中央的 **转发管理服务器 (MS)** 注册自身信息。其管理的KVM TX设备信息由对应的 **KVM管理服务器** 上报给MS。
*   **转发管理服务器 (Forwarder Management Server - 中央)**：
    *   维护所有 **转发节点** 的状态和地址信息。
    *   维护由各个 **KVM管理服务器** 注册的KVM TX设备信息及其归属的KVM管理服务器和推荐的接入转发节点。
    *   响应来自 **KVM管理服务器** 的路由查询请求，提供KVM TX设备的路由信息。
    *   （可选）负责部分全局配置管理和认证授权策略。
*   **KVM TX设备**：
    *   由其所在局域网的 **KVM管理服务器** 管理。
    *   与本地的 **转发节点** 建立稳定的连接，通过该连接与KVM RX设备进行数据交互。
*   **客户端(可选)**：
    *   通过互联网连接的移动端、PC端或网页端的应用。
    *   与指定的 **KVM管理服务器 (KVM Local Management Server)** 建立连接，获取可访问的KVM TX设备信息，发起对特定KVM TX设备的访问请求。
    *   从KVM管理服务器获取连接参数后，与指定的 **转发节点** 提供建立WebRTC连接。
    *   本次设计不对客户端做详细描述。
### 2.3 技术选型
*   **核心转发引擎**：mediasoup (C++ 库，提供 WebRTC SFU 功能)
*   **主要编程语言**：Rust (用于开发转发节点和转发管理服务器，保证性能和安全)
*   **异步运行时**：Tokio (Rust 的异步编程框架)
*   **节点间/客户端-节点/管理API**：RESTful API (Axum)
*   **数据库 (转发管理服务器)**：etcd (用于存储节点和目标设备信息)

## 3. 模块详细设计

### 3.1 转发节点 (Forwarder Node)

转发节点是KVM隧道服务的核心组件，负责处理所有数据转发和路由功能。

#### 3.1.1 功能概述
*   **数据转发**：使用mediasoup实现音视频流转发，支持WebRTC、组播等多种传输方式
*   **节点间通信**：通过mediasoup的PipeTransport实现节点间高效数据传输
*   **设备连接管理**：管理与KVM TX/RX设备的TCP/UDP连接
*   **会话管理**：维护转发会话的生命周期和状态
*   **负载均衡**：支持多节点间的负载分担和故障转移

#### 3.1.2 主要特性
- **高性能**：基于Rust实现，利用零拷贝技术和异步处理
- **可扩展**：支持水平扩展和动态负载均衡
- **高可用**：具备故障检测和自动恢复能力
- **安全性**：提供端到端加密和访问控制
- **监控性**：内置指标收集和日志记录功能

### 3.2 转发管理服务器 (Forwarder Management Server)

#### 3.2.1 功能描述
*   **区域管理(一个局域网对应一个区域)**：
    *   维护各个转发节点与KVM管理服务器的区域信息
*   **转发节点注册与状态管理**：
    *   接收转发节点的注册请求，记录节点ID、IP地址、端口、能力等。
    *   处理节点心跳，更新节点状态（在线、离线、负载等）。
    *   定期清理无效或超时的节点。
*   **KVM TX设备注册与归属管理**：
    *   接收 **KVM管理服务器** 的注册申请。
    *   维护 `TargetID -> NodeID` 的映射关系。
*   **路由信息管理**：
    *   接收 **KVM管理服务器** 的转发开始/结束申请，维护路由与**KVM管理服务器**的关系。
*   **配置管理 (可选)**：
    *   提供全局配置，如 mediasoup 参数模板、安全策略等。

#### 3.2.2 核心流程 (Mermaid)

##### 转发节点上线/下线
```mermaid
sequenceDiagram
    participant FN as 转发节点
    participant MS as 转发管理服务器

    FN->>+MS: 注册请求 (NodeInfo)
    MS->>MS: 存储/更新 NodeInfo
    MS-->>-FN: 注册成功
    
    loop 心跳
        FN->>MS: 心跳包
        MS->>MS: 更新节点最后心跳时间
    end

    Note over FN, MS: 若MS长时间未收到FN心跳
    MS->>MS: 标记FN为离线/不可用
    MS->>MS: (可选)触发告警，处理FN上目标设备的迁移逻辑
```

### 3.3 KVM管理服务器 (KVM Local Management Server)

#### 3.3.1 功能描述
*   **设备管理**：
    *   管理本局域网内的KVM RX设备和KVM TX设备的注册、认证和状态监控。
    *   维护设备清单和权限映射关系。
    *   处理设备上线/下线事件。
*   **访问请求处理**：
    *   接收来自本局域网内KVM RX设备或客户端的访问请求。
    *   验证访问权限和策略。
    *   协调本地访问（同局域网内）或跨网络访问。
*   **与转发管理服务器交互**：
    *   注册本地可对外提供访问的KVM TX设备信息。
    *   查询外部KVM TX设备的路由信息。
    *   同步全局配置和策略。

#### 3.3.2 核心流程 (Mermaid)

##### KVM RX设备/客户端请求访问流程
不考虑访问本地KVM TX设备的情况。
```mermaid
sequenceDiagram
    participant C as KVM RX设备/客户端
    participant KMS as KVM管理服务器
    participant MS as 转发管理服务器
    participant FN as 转发节点

    C->>+KMS: 请求访问 TargetID
    KMS->>KMS: 验证客户端身份和权限
    
    KMS->>+MS: 请求转发
    MS->>MS: 查找设备所在节点
    MS->>+FN: 请求转发
    FN->>-MS: 返回目标虚拟IP
    MS-->>-KMS: 返回目标虚拟IP
    KMS-->>-C: 返回目标虚拟IP
    
    C->>FN: 使用虚拟IP建立连接
    Note over C,FN: 后续按照标准转发流程进行
```

##### KVM TX设备注册到转发服务器
```mermaid
sequenceDiagram
    actor USER as 用户
    participant KMS as KVM管理服务器
    participant T as KVM TX设备
    participant MS as 转发管理服务器
    participant FN as 转发节点

    T->>+KMS: 设备上线
    KMS->>KMS: 验证设备，更新本地设备清单
    USER ->>+KMS: 请求注册允许外部访问的设备列表
    KMS->>+MS: 注册设备信息 (TargetID, KMS ID)
    MS->>MS: 存储设备信息，标记为可访问
    MS-->>-KMS: 注册成功
    KMS-->>-USER: 返回注册成功信息

    T-->>KMS: 设备下线
    KMS->>KMS: 更新设备状态
    KMS->>+MS: 更新设备下线信息 (TargetID)
    MS->>FN: 暂停转发
    MS-->>-KMS: 更新成功

    T-->>KMS: 设备上线
    KMS->>KMS: 更新设备状态
    KMS->>+MS: 更新设备上线信息 (TargetID)
    MS->>FN: 恢复转发
    MS-->>-KMS: 更新成功
```

### 3.4 KVM RX设备

#### 3.4.1 功能描述
*   **向KVM管理服务器发起访问请求**：
    *   首先与本局域网的KVM管理服务器建立连接并完成认证。
    *   发送访问特定KVM TX设备的请求，包含目标设备ID和所需权限。
*   **连接到转发节点**：
    *   根据KVM管理服务器返回的KVM TX设备的虚拟IP建立连接。
*   **与KVM TX设备进行数据交互**：
    *   监听KVM TX设备的流媒体组播。
    *   向KVM TX设备发送UDP数据（音视频、键盘鼠标事件）。
    *   通过HTTP协议获取KVM TX设备状态。

### 3.5 客户端
预留，暂不设计。

## 4. API 设计概述

### 4.1 转发节点API
转发节点提供RESTful API用于会话管理、状态查询和节点间通信。主要包括：
- **会话管理**：创建、查询、销毁转发会话
- **节点管理**：节点注册、心跳、状态上报
- **监控接口**：性能指标和健康状态查询

### 4.2 转发管理服务器API
提供全局管理和协调功能：
```http
# 节点注册
POST /api/v1/nodes/register

# 设备路由查询  
GET /api/v1/routing/devices/{device_id}

# 转发请求
POST /api/v1/forwarding/request
```

### 4.3 KVM管理服务器API
管理本地KVM设备：
```http
# 设备注册
POST /api/v1/devices/register

# 访问请求
POST /api/v1/access/request

# 设备状态查询
GET /api/v1/devices/{device_id}/status
```

## 5. 数据流设计

### 5.1 媒体数据流
#### 5.1.1 KVM TX到KVM RX的媒体流
```mermaid
flowchart LR
    subgraph "KVM TX设备端"
        TX[KVM TX设备]
        TX_MC_ADDR[组播地址<br/>*************:5000]
    end
    
    subgraph "转发节点B - TX接入"
        subgraph "组播接收层"
            MC_RX[组播接收器]
        end
        
        subgraph "MediaSoup核心B"
            PT_RX[PlainTransport<br/>Producer]
            ROUTER_B[Router B]
            CONSUMER_B[PipeTransport<br/>Consumer]
        end
    end
    
    subgraph "转发节点A - RX接入"
        subgraph "MediaSoup核心A"
            PRODUCER_A[PipeTransport<br/>Producer]
            ROUTER_A[Router A]
            PT_TX[PlainTransport<br/>Consumer]
        end
        
        subgraph "组播生成层"
            MC_TX[组播生成器]
            RX_MC_ADDR[组播地址<br/>*************:5000]
        end
    end
    
    subgraph "KVM RX设备端组播消费"
        RX[KVM RX设备]
    end
    
    TX --> TX_MC_ADDR
    TX_MC_ADDR -.->|UDP组播| MC_RX
    MC_RX --> PT_RX
    PT_RX --> ROUTER_B
    ROUTER_B --> CONSUMER_B
    CONSUMER_B --> PRODUCER_A
    PRODUCER_A --> ROUTER_A
    ROUTER_A --> PT_TX
    PT_TX --> MC_TX
    MC_TX --> RX_MC_ADDR
    RX_MC_ADDR -.->|UDP组播| RX
    
    style TX fill:#ffebee
    style RX fill:#e8f5e8
    style ROUTER_B fill:#e3f2fd
    style ROUTER_A fill:#e3f2fd
```

#### 5.1.2 控制数据流 (键盘鼠标)
```mermaid
flowchart RL
    subgraph "KVM RX设备端"
        RX[KVM RX设备]
        RX_UDP[UDP数据]
    end
    
    subgraph "转发节点A (KVM RX接入)"
        FN_A_UDP[UDP隧道]
    end
    
    subgraph "转发节点B (KVM TX接入)"
        FN_B_UDP[UDP隧道]
    end
    
    subgraph "KVM TX设备端"
        TX_UDP[UDP数据]
        TX[KVM TX设备]
    end
    
    RX --> RX_UDP
    RX_UDP --> FN_A_UDP
    FN_A_UDP -.->|跨节点隧道| FN_B_UDP
    FN_B_UDP --> TX_UDP
    TX_UDP --> TX
    
    style RX fill:#ccffcc
    style TX fill:#ffcccc
```

## 6. 部署架构

### 6.1 典型部署拓扑

#### 6.1.2 多区域部署
```mermaid
graph TB
    subgraph "区域1 - 北京"
        subgraph "北京数据中心"
            BJ_TX[KVM TX设备]
            BJ_FN[转发节点群]
            BJ_KMS[KVM管理服务器]
        end
    end
    
    subgraph "区域2 - 上海"
        subgraph "上海数据中心"
            SH_TX[KVM TX设备]
            SH_FN[转发节点群]
            SH_KMS[KVM管理服务器]
        end
    end
    
    subgraph "区域3 - 广州"
        subgraph "广州数据中心"
            GZ_TX[KVM TX设备]
            GZ_FN[转发节点群]
            GZ_KMS[KVM管理服务器]
        end
    end
    
    subgraph "中央管理"
        MS[转发管理服务器]
        ETCD[(etcd集群)]
        MON[全局监控]
    end
    
    subgraph "客户端区域"
        CLIENT1[客户端1]
        CLIENT2[客户端2]
        CLIENTN[客户端N]
    end
    
    BJ_KMS --- MS
    SH_KMS --- MS
    GZ_KMS --- MS
    MS --- ETCD
    
    BJ_FN -.->|跨区域连接| SH_FN
    SH_FN -.->|跨区域连接| GZ_FN
    GZ_FN -.->|跨区域连接| BJ_FN
    
    CLIENT1 -.->|就近接入| BJ_FN
    CLIENT2 -.->|就近接入| SH_FN
    CLIENTN -.->|就近接入| GZ_FN
    
    MON --- BJ_FN
    MON --- SH_FN
    MON --- GZ_FN
```

### 6.2 容器化部署

#### 6.2.1 Docker容器配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  forwarder-node:
    image: kvm-tunnel/forwarder-node:latest
    ports:
      - "8080:8080"      # API端口
      - "50000-50100:50000-50100/udp"  # MediaSoup端口范围
    environment:
      - NODE_ID=${NODE_ID}
      - REGION=${REGION}
      - MANAGEMENT_SERVER_URL=${MS_URL}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - ./config:/etc/kvm-tunnel
      - ./logs:/var/log/kvm-tunnel
    networks:
      - kvm-tunnel-network
    depends_on:
      - management-server

  management-server:
    image: kvm-tunnel/management-server:latest
    ports:
      - "9090:9090"
    environment:
      - ETCD_ENDPOINTS=${ETCD_ENDPOINTS}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - ./config:/etc/kvm-tunnel
      - ./logs:/var/log/kvm-tunnel
    networks:
      - kvm-tunnel-network
    depends_on:
      - etcd

  etcd:
    image: quay.io/coreos/etcd:v3.5.0
    ports:
      - "2379:2379"
      - "2380:2380"
    environment:
      - ETCD_NAME=etcd-server
      - ETCD_DATA_DIR=/etcd-data
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:2379
      - ETCD_ADVERTISE_CLIENT_URLS=http://etcd:2379
      - ETCD_LISTEN_PEER_URLS=http://0.0.0.0:2380
      - ETCD_INITIAL_ADVERTISE_PEER_URLS=http://etcd:2380
      - ETCD_INITIAL_CLUSTER=etcd-server=http://etcd:2380
    volumes:
      - etcd-data:/etcd-data
    networks:
      - kvm-tunnel-network

networks:
  kvm-tunnel-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  etcd-data:
```

### 6.3 高可用性设计

#### 6.3.1 转发节点高可用
```mermaid
graph TB
    subgraph "高可用转发节点集群"
        subgraph "主节点组"
            FN1[转发节点1<br/>Active]
            FN2[转发节点2<br/>Active]
        end
        
        subgraph "备份节点组"
            FN3[转发节点3<br/>Standby]
            FN4[转发节点4<br/>Standby]
        end
        
        subgraph "负载均衡层"
            LB1[负载均衡器1]
            LB2[负载均衡器2]
            VIP[虚拟IP]
        end
    end
    
    subgraph "健康检查"
        HC[健康检查服务]
        MON[监控告警]
    end
    
    VIP --- LB1
    VIP --- LB2
    LB1 --- FN1
    LB1 --- FN2
    LB2 --- FN1
    LB2 --- FN2
    
    HC --- FN1
    HC --- FN2
    HC --- FN3
    HC --- FN4
    HC --- MON
    
    FN3 -.->|故障切换| FN1
    FN4 -.->|故障切换| FN2
    
    style FN1 fill:#e8f5e8
    style FN2 fill:#e8f5e8
    style FN3 fill:#fff3e0
    style FN4 fill:#fff3e0
    style VIP fill:#ffebee
```

#### 6.3.2 故障转移机制
```mermaid
sequenceDiagram
    participant HC as 健康检查
    participant LB as 负载均衡器
    participant FN1 as 转发节点1
    participant FN2 as 转发节点2
    participant MS as 管理服务器
    participant CLIENT as 客户端

    Note over FN1: 节点故障
    HC->>FN1: 健康检查请求
    FN1-->>HC: 无响应
    HC->>FN1: 重试健康检查
    FN1-->>HC: 仍无响应
    
    HC->>LB: 标记FN1为不健康
    LB->>LB: 从负载均衡池移除FN1
    HC->>MS: 报告FN1故障
    
    MS->>FN2: 请求接管FN1的会话
    FN2->>FN2: 重建会话状态
    FN2->>MS: 会话接管完成
    
    CLIENT->>LB: 新请求
    LB->>FN2: 路由到FN2
    FN2->>CLIENT: 正常响应
    
    Note over FN1: 节点恢复
    FN1->>HC: 健康检查响应
    HC->>LB: 标记FN1为健康
    LB->>LB: 将FN1加回负载均衡池
    HC->>MS: 报告FN1恢复
```

## 7. 安全考虑

### 7.1 安全机制概述
- **传输加密**：TLS/DTLS、SRTP加密
- **身份认证**：JWT令牌管理
- **访问控制**：基于规则的访问控制列表
- **审计日志**：安全事件记录和追踪

### 7.2 数据保护
- **端到端加密**：媒体流和控制信令加密
- **密钥管理**：支持密钥轮换和托管

---

*本设计文档将随着项目的发展持续更新和完善。*

