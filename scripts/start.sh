#!/bin/bash

# KVM隧道转发节点启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
DEFAULT_CONFIG="/etc/kvm_tunnel/config.yaml"
DEFAULT_LOG_LEVEL="info"
DEFAULT_BIND_IP="0.0.0.0"
DEFAULT_API_PORT="8080"

# 显示帮助信息
show_help() {
    cat << EOF
KVM隧道转发节点启动脚本

用法: $0 [选项]

选项:
    -c, --config FILE       配置文件路径 (默认: $DEFAULT_CONFIG)
    -l, --log-level LEVEL   日志级别 (默认: $DEFAULT_LOG_LEVEL)
    -b, --bind-ip IP        绑定IP地址 (默认: $DEFAULT_BIND_IP)
    -p, --port PORT         API端口 (默认: $DEFAULT_API_PORT)
    -d, --daemon            以守护进程模式运行
    -h, --help              显示此帮助信息

示例:
    $0                                          # 使用默认配置启动
    $0 -c /path/to/config.yaml                 # 使用指定配置文件
    $0 -l debug -p 9080                        # 设置日志级别和端口
    $0 -d                                       # 以守护进程模式运行

EOF
}

# 解析命令行参数
CONFIG_FILE="$DEFAULT_CONFIG"
LOG_LEVEL="$DEFAULT_LOG_LEVEL"
BIND_IP="$DEFAULT_BIND_IP"
API_PORT="$DEFAULT_API_PORT"
DAEMON_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -l|--log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        -b|--bind-ip)
            BIND_IP="$2"
            shift 2
            ;;
        -p|--port)
            API_PORT="$2"
            shift 2
            ;;
        -d|--daemon)
            DAEMON_MODE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查二进制文件
BINARY_PATH="./target/release/kvm_tunnel"
if [[ ! -f "$BINARY_PATH" ]]; then
    log_error "找不到二进制文件: $BINARY_PATH"
    log_info "请先运行: cargo build --release"
    exit 1
fi

# 检查配置文件
if [[ ! -f "$CONFIG_FILE" ]]; then
    log_warn "配置文件不存在: $CONFIG_FILE"
    log_info "将使用默认配置"
fi

# 设置环境变量
export KVM_TUNNEL_CONFIG="$CONFIG_FILE"
export RUST_LOG="kvm_tunnel=$LOG_LEVEL"

# 创建必要的目录
log_info "创建必要的目录..."
sudo mkdir -p /var/log/kvm_tunnel
sudo mkdir -p /var/lib/kvm_tunnel
sudo mkdir -p /etc/kvm_tunnel

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tuln | grep -q ":$port "; then
        log_error "端口 $port 已被占用"
        log_info "请检查是否有其他服务正在使用此端口"
        exit 1
    fi
}

log_info "检查端口可用性..."
check_port "$API_PORT"

# 显示启动信息
log_info "启动KVM隧道转发节点"
log_info "配置文件: $CONFIG_FILE"
log_info "日志级别: $LOG_LEVEL"
log_info "绑定IP: $BIND_IP"
log_info "API端口: $API_PORT"
log_info "守护进程模式: $DAEMON_MODE"

# 启动节点
if [[ "$DAEMON_MODE" == true ]]; then
    log_info "以守护进程模式启动..."
    nohup "$BINARY_PATH" > /var/log/kvm_tunnel/daemon.log 2>&1 &
    PID=$!
    echo $PID > /var/run/kvm_tunnel.pid
    log_info "节点已启动，PID: $PID"
    log_info "日志文件: /var/log/kvm_tunnel/daemon.log"
    log_info "PID文件: /var/run/kvm_tunnel.pid"
else
    log_info "启动节点..."
    exec "$BINARY_PATH"
fi
