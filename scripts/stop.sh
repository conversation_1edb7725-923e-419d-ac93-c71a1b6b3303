#!/bin/bash

# KVM隧道转发节点停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# PID文件路径
PID_FILE="/var/run/kvm_tunnel.pid"

# 显示帮助信息
show_help() {
    cat << EOF
KVM隧道转发节点停止脚本

用法: $0 [选项]

选项:
    -f, --force             强制停止（使用SIGKILL）
    -h, --help              显示此帮助信息

示例:
    $0                      # 正常停止节点
    $0 -f                   # 强制停止节点

EOF
}

# 解析命令行参数
FORCE_STOP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--force)
            FORCE_STOP=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查是否有运行中的进程
check_running_processes() {
    local pids=$(pgrep -f "kvm_tunnel" || true)
    if [[ -n "$pids" ]]; then
        log_info "发现运行中的KVM隧道进程:"
        ps -p $pids -o pid,ppid,cmd --no-headers
        return 0
    else
        return 1
    fi
}

# 停止指定PID的进程
stop_process() {
    local pid=$1
    local signal=${2:-TERM}
    
    if kill -0 "$pid" 2>/dev/null; then
        log_info "发送 SIG$signal 信号到进程 $pid"
        kill -$signal "$pid"
        
        # 等待进程停止
        local count=0
        while kill -0 "$pid" 2>/dev/null && [[ $count -lt 30 ]]; do
            sleep 1
            ((count++))
        done
        
        if kill -0 "$pid" 2>/dev/null; then
            log_warn "进程 $pid 在30秒后仍在运行"
            return 1
        else
            log_info "进程 $pid 已停止"
            return 0
        fi
    else
        log_warn "进程 $pid 不存在或已停止"
        return 0
    fi
}

# 主停止逻辑
main() {
    log_info "停止KVM隧道转发节点"
    
    # 检查PID文件
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        log_info "从PID文件读取到进程ID: $pid"
        
        if [[ "$FORCE_STOP" == true ]]; then
            stop_process "$pid" "KILL"
        else
            if ! stop_process "$pid" "TERM"; then
                log_warn "正常停止失败，尝试强制停止"
                stop_process "$pid" "KILL"
            fi
        fi
        
        # 删除PID文件
        rm -f "$PID_FILE"
        log_info "已删除PID文件: $PID_FILE"
    else
        log_warn "PID文件不存在: $PID_FILE"
    fi
    
    # 检查是否还有其他运行中的进程
    if check_running_processes; then
        log_warn "发现其他运行中的KVM隧道进程"
        
        if [[ "$FORCE_STOP" == true ]]; then
            log_info "强制停止所有KVM隧道进程"
            pkill -KILL -f "kvm_tunnel" || true
        else
            log_info "尝试正常停止所有KVM隧道进程"
            pkill -TERM -f "kvm_tunnel" || true
            
            # 等待进程停止
            sleep 5
            
            if check_running_processes; then
                log_warn "仍有进程在运行，强制停止"
                pkill -KILL -f "kvm_tunnel" || true
            fi
        fi
    fi
    
    # 最终检查
    if check_running_processes; then
        log_error "停止失败，仍有进程在运行"
        exit 1
    else
        log_info "所有KVM隧道进程已停止"
    fi
    
    # 清理临时文件
    log_info "清理临时文件"
    rm -f /tmp/kvm_tunnel_*.sock || true
    rm -f /var/run/kvm_tunnel_*.pid || true
    
    log_info "停止完成"
}

# 执行主函数
main
