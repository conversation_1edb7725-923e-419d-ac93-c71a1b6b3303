# KVM隧道转发节点 Makefile

# 项目信息
PROJECT_NAME := kvm_tunnel
VERSION := 0.1.0
BINARY_NAME := kvm_tunnel

# 路径配置
TARGET_DIR := target
RELEASE_DIR := $(TARGET_DIR)/release
DEBUG_DIR := $(TARGET_DIR)/debug
CONFIG_DIR := config
SCRIPTS_DIR := scripts

# 构建配置
CARGO := cargo
CARGO_FLAGS := 
RELEASE_FLAGS := --release
TEST_FLAGS := 

# 安装路径
INSTALL_PREFIX := /usr/local
BIN_DIR := $(INSTALL_PREFIX)/bin
CONFIG_INSTALL_DIR := /etc/$(PROJECT_NAME)
LOG_DIR := /var/log/$(PROJECT_NAME)
DATA_DIR := /var/lib/$(PROJECT_NAME)

# 默认目标
.PHONY: all
all: build

# 构建目标
.PHONY: build
build:
	@echo "构建项目..."
	$(CARGO) build $(CARGO_FLAGS)

.PHONY: release
release:
	@echo "构建发布版本..."
	$(CARGO) build $(RELEASE_FLAGS)

.PHONY: debug
debug:
	@echo "构建调试版本..."
	$(CARGO) build

# 测试目标
.PHONY: test
test:
	@echo "运行测试..."
	$(CARGO) test $(TEST_FLAGS)

.PHONY: test-unit
test-unit:
	@echo "运行单元测试..."
	$(CARGO) test --lib

.PHONY: test-integration
test-integration:
	@echo "运行集成测试..."
	$(CARGO) test --test integration_tests

# 代码质量
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	$(CARGO) fmt

.PHONY: clippy
clippy:
	@echo "运行Clippy检查..."
	$(CARGO) clippy -- -D warnings

.PHONY: check
check:
	@echo "检查代码..."
	$(CARGO) check

# 清理目标
.PHONY: clean
clean:
	@echo "清理构建文件..."
	$(CARGO) clean

.PHONY: clean-all
clean-all: clean
	@echo "清理所有生成文件..."
	rm -rf $(TARGET_DIR)
	rm -f Cargo.lock

# 运行目标
.PHONY: run
run: build
	@echo "运行项目..."
	$(CARGO) run

.PHONY: run-release
run-release: release
	@echo "运行发布版本..."
	./$(RELEASE_DIR)/$(BINARY_NAME)

# 开发目标
.PHONY: dev
dev:
	@echo "开发模式（自动重新构建）..."
	$(CARGO) watch -x run

# 安装目标
.PHONY: install
install: release
	@echo "安装到系统..."
	sudo mkdir -p $(BIN_DIR)
	sudo mkdir -p $(CONFIG_INSTALL_DIR)
	sudo mkdir -p $(LOG_DIR)
	sudo mkdir -p $(DATA_DIR)
	sudo cp $(RELEASE_DIR)/$(BINARY_NAME) $(BIN_DIR)/
	sudo cp $(CONFIG_DIR)/example.yaml $(CONFIG_INSTALL_DIR)/config.yaml.example
	sudo cp $(SCRIPTS_DIR)/*.sh $(BIN_DIR)/
	sudo chmod +x $(BIN_DIR)/$(BINARY_NAME)
	sudo chmod +x $(BIN_DIR)/*.sh
	@echo "安装完成！"
	@echo "二进制文件: $(BIN_DIR)/$(BINARY_NAME)"
	@echo "配置目录: $(CONFIG_INSTALL_DIR)"
	@echo "日志目录: $(LOG_DIR)"
	@echo "数据目录: $(DATA_DIR)"

.PHONY: uninstall
uninstall:
	@echo "卸载系统安装..."
	sudo rm -f $(BIN_DIR)/$(BINARY_NAME)
	sudo rm -f $(BIN_DIR)/start.sh
	sudo rm -f $(BIN_DIR)/stop.sh
	sudo rm -rf $(CONFIG_INSTALL_DIR)
	@echo "卸载完成！"
	@echo "注意: 日志和数据目录未删除"

# 打包目标
.PHONY: package
package: release
	@echo "创建发布包..."
	mkdir -p dist
	tar -czf dist/$(PROJECT_NAME)-$(VERSION)-linux-x86_64.tar.gz \
		-C $(RELEASE_DIR) $(BINARY_NAME) \
		-C ../../$(CONFIG_DIR) example.yaml \
		-C ../$(SCRIPTS_DIR) start.sh stop.sh \
		-C .. README.md
	@echo "发布包已创建: dist/$(PROJECT_NAME)-$(VERSION)-linux-x86_64.tar.gz"

# Docker目标
.PHONY: docker-build
docker-build:
	@echo "构建Docker镜像..."
	docker build -t $(PROJECT_NAME):$(VERSION) .
	docker tag $(PROJECT_NAME):$(VERSION) $(PROJECT_NAME):latest

.PHONY: docker-run
docker-run:
	@echo "运行Docker容器..."
	docker run --rm -p 8080:8080 $(PROJECT_NAME):latest

# 文档目标
.PHONY: doc
doc:
	@echo "生成文档..."
	$(CARGO) doc --no-deps --open

# 基准测试
.PHONY: bench
bench:
	@echo "运行基准测试..."
	$(CARGO) bench

# 依赖管理
.PHONY: update
update:
	@echo "更新依赖..."
	$(CARGO) update

.PHONY: audit
audit:
	@echo "安全审计..."
	$(CARGO) audit

# 帮助信息
.PHONY: help
help:
	@echo "KVM隧道转发节点 Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  build          - 构建项目"
	@echo "  release        - 构建发布版本"
	@echo "  debug          - 构建调试版本"
	@echo "  test           - 运行所有测试"
	@echo "  test-unit      - 运行单元测试"
	@echo "  test-integration - 运行集成测试"
	@echo "  fmt            - 格式化代码"
	@echo "  clippy         - 运行Clippy检查"
	@echo "  check          - 检查代码"
	@echo "  clean          - 清理构建文件"
	@echo "  clean-all      - 清理所有生成文件"
	@echo "  run            - 运行项目"
	@echo "  run-release    - 运行发布版本"
	@echo "  dev            - 开发模式（自动重新构建）"
	@echo "  install        - 安装到系统"
	@echo "  uninstall      - 卸载系统安装"
	@echo "  package        - 创建发布包"
	@echo "  docker-build   - 构建Docker镜像"
	@echo "  docker-run     - 运行Docker容器"
	@echo "  doc            - 生成文档"
	@echo "  bench          - 运行基准测试"
	@echo "  update         - 更新依赖"
	@echo "  audit          - 安全审计"
	@echo "  help           - 显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make release   # 构建发布版本"
	@echo "  make test      # 运行测试"
	@echo "  make install   # 安装到系统"
